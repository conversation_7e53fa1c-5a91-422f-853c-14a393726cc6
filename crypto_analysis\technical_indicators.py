"""
Technical Indicators - Pure NumPy Implementation
Common technical analysis indicators for crypto trading
"""
import numpy as np

class TechnicalIndicators:
    """Technical analysis indicators using pure NumPy"""
    
    @staticmethod
    def simple_moving_average(prices, window):
        """Calculate Simple Moving Average (SMA)"""
        return np.convolve(prices, np.ones(window)/window, mode='valid')
    
    @staticmethod
    def exponential_moving_average(prices, window):
        """Calculate Exponential Moving Average (EMA)"""
        alpha = 2 / (window + 1)
        ema = np.zeros_like(prices)
        ema[0] = prices[0]
        
        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    @staticmethod
    def rsi(prices, window=14):
        """
        Calculate Relative Strength Index (RSI)
        Args:
            prices: Price array
            window: Period for RSI calculation
        Returns:
            RSI values (0-100)
        """
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        # Calculate average gains and losses
        avg_gains = np.zeros(len(gains))
        avg_losses = np.zeros(len(losses))
        
        # Initial averages
        avg_gains[window-1] = np.mean(gains[:window])
        avg_losses[window-1] = np.mean(losses[:window])
        
        # Smoothed averages
        for i in range(window, len(gains)):
            avg_gains[i] = (avg_gains[i-1] * (window-1) + gains[i]) / window
            avg_losses[i] = (avg_losses[i-1] * (window-1) + losses[i]) / window
        
        # Calculate RSI
        rs = avg_gains / (avg_losses + 1e-10)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(prices, fast_period=12, slow_period=26, signal_period=9):
        """
        Calculate MACD (Moving Average Convergence Divergence)
        Returns:
            macd_line, signal_line, histogram
        """
        ema_fast = TechnicalIndicators.exponential_moving_average(prices, fast_period)
        ema_slow = TechnicalIndicators.exponential_moving_average(prices, slow_period)
        
        # MACD line
        macd_line = ema_fast - ema_slow
        
        # Signal line
        signal_line = TechnicalIndicators.exponential_moving_average(macd_line, signal_period)
        
        # Histogram
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(prices, window=20, num_std=2):
        """
        Calculate Bollinger Bands
        Returns:
            upper_band, middle_band (SMA), lower_band
        """
        sma = TechnicalIndicators.simple_moving_average(prices, window)
        
        # Calculate rolling standard deviation
        rolling_std = np.zeros(len(prices) - window + 1)
        for i in range(len(rolling_std)):
            rolling_std[i] = np.std(prices[i:i+window])
        
        upper_band = sma + (rolling_std * num_std)
        lower_band = sma - (rolling_std * num_std)
        
        return upper_band, sma, lower_band
    
    @staticmethod
    def stochastic_oscillator(high, low, close, k_period=14, d_period=3):
        """
        Calculate Stochastic Oscillator
        Args:
            high, low, close: Price arrays
            k_period: Period for %K calculation
            d_period: Period for %D calculation
        Returns:
            %K, %D values
        """
        k_values = np.zeros(len(close))
        
        for i in range(k_period-1, len(close)):
            highest_high = np.max(high[i-k_period+1:i+1])
            lowest_low = np.min(low[i-k_period+1:i+1])
            
            if highest_high != lowest_low:
                k_values[i] = 100 * (close[i] - lowest_low) / (highest_high - lowest_low)
            else:
                k_values[i] = 50  # Neutral value when no range
        
        # %D is SMA of %K
        d_values = TechnicalIndicators.simple_moving_average(k_values[k_period-1:], d_period)
        
        return k_values[k_period-1:], d_values
    
    @staticmethod
    def williams_r(high, low, close, period=14):
        """
        Calculate Williams %R
        Args:
            high, low, close: Price arrays
            period: Lookback period
        Returns:
            Williams %R values (-100 to 0)
        """
        wr_values = np.zeros(len(close))
        
        for i in range(period-1, len(close)):
            highest_high = np.max(high[i-period+1:i+1])
            lowest_low = np.min(low[i-period+1:i+1])
            
            if highest_high != lowest_low:
                wr_values[i] = -100 * (highest_high - close[i]) / (highest_high - lowest_low)
            else:
                wr_values[i] = -50  # Neutral value
        
        return wr_values[period-1:]
    
    @staticmethod
    def atr(high, low, close, period=14):
        """
        Calculate Average True Range (ATR)
        Measures volatility
        """
        # True Range calculation
        tr1 = high[1:] - low[1:]  # High - Low
        tr2 = np.abs(high[1:] - close[:-1])  # |High - Previous Close|
        tr3 = np.abs(low[1:] - close[:-1])   # |Low - Previous Close|
        
        true_range = np.maximum(tr1, np.maximum(tr2, tr3))
        
        # Calculate ATR as SMA of True Range
        atr = TechnicalIndicators.simple_moving_average(true_range, period)
        
        return atr
    
    @staticmethod
    def obv(close, volume):
        """
        Calculate On-Balance Volume (OBV)
        Args:
            close: Closing prices
            volume: Trading volumes
        Returns:
            OBV values
        """
        obv = np.zeros(len(close))
        obv[0] = volume[0]
        
        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv[i] = obv[i-1] + volume[i]
            elif close[i] < close[i-1]:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]
        
        return obv
