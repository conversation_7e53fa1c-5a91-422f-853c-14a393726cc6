@echo off
echo 📦 Installing Crypto ML System Requirements...
echo =============================================

REM Set Python path
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

REM Check if Python exists
if not exist "%PYTHON_PATH%" (
    echo ❌ Python not found at %PYTHON_PATH%
    echo Please check your Python installation
    pause
    exit /b 1
)

echo 🐍 Using Python at: %PYTHON_PATH%
echo.

REM Upgrade pip first
echo 📈 Upgrading pip...
"%PYTHON_PATH%" -m pip install --upgrade pip

echo.
echo 📦 Installing required packages...
echo.

REM Install numpy
echo Installing NumPy...
"%PYTHON_PATH%" -m pip install numpy==1.24.3

REM Install requests
echo Installing Requests...
"%PYTHON_PATH%" -m pip install requests==2.31.0

REM Install matplotlib
echo Installing Matplotlib...
"%PYTHON_PATH%" -m pip install matplotlib==3.7.1

echo.
echo ✅ All packages installed successfully!
echo.

REM Verify installation
echo 🧪 Verifying installation...
"%PYTHON_PATH%" -c "import numpy as np; print('✅ NumPy version:', np.__version__)"
"%PYTHON_PATH%" -c "import requests; print('✅ Requests version:', requests.__version__)"
"%PYTHON_PATH%" -c "import matplotlib; print('✅ Matplotlib version:', matplotlib.__version__)"

echo.
echo 🎉 Setup completed! You can now run:
echo    .\run_demo.bat
echo    .\run_main.bat
echo    .\run_test.bat
echo.
pause
