# Panduan Penggunaan Crypto ML System

## 🚀 Quick Start

### 1. Instalasi
```bash
# Clone atau download project
cd ML

# Install dependencies
pip install numpy requests matplotlib
```

### 2. <PERSON><PERSON><PERSON><PERSON> Demo
```bash
# Demo lengkap sistem
python demo.py

# Analisis lengkap Bitcoin
python main.py

# Test sistem
python test_system.py
```

## 📊 Komponen Utama

### 1. Data Fetcher
```python
from crypto_analysis.data_fetcher import CryptoDataFetcher

fetcher = CryptoDataFetcher()

# Fetch data Bitcoin 30 hari terakhir
data = fetcher.fetch_price_history('bitcoin', days=30)

# Fetch multiple cryptocurrencies
multi_data = fetcher.get_multiple_symbols(['bitcoin', 'ethereum'], days=30)
```

### 2. Technical Indicators
```python
from crypto_analysis.technical_indicators import TechnicalIndicators

indicators = TechnicalIndicators()
prices = data['prices']

# Moving Averages
sma_20 = indicators.simple_moving_average(prices, 20)
ema_12 = indicators.exponential_moving_average(prices, 12)

# Momentum Indicators
rsi = indicators.rsi(prices, window=14)
macd_line, signal_line, histogram = indicators.macd(prices)

# Volatility Indicators
upper_bb, middle_bb, lower_bb = indicators.bollinger_bands(prices)
atr = indicators.atr(highs, lows, closes)

# Volume Indicators
obv = indicators.obv(closes, volumes)
```

### 3. Machine Learning Models

#### Linear Regression (Price Prediction)
```python
from ml_core.linear_regression import LinearRegression

# Prepare features
features = np.column_stack([sma_values, rsi_values, volume_ratios])
target = price_changes

# Train model
model = LinearRegression(learning_rate=0.01, max_iterations=1000)
model.fit(features, target)

# Make predictions
predictions = model.predict(new_features)
score = model.score(test_features, test_target)

# Predict future trend
future_predictions = model.predict_trend(current_features, periods=5)
```

#### Logistic Regression (Trading Signals)
```python
from ml_core.logistic_regression import LogisticRegression

# Binary target: 1 for price increase, 0 for decrease
binary_target = (price_changes > 0).astype(int)

# Train model
signal_model = LogisticRegression(learning_rate=0.1)
signal_model.fit(features, binary_target)

# Generate trading signals
signals = signal_model.generate_trading_signals(
    features, 
    buy_threshold=0.7, 
    sell_threshold=0.3
)
# Returns: 1=Buy, -1=Sell, 0=Hold

# Get probabilities
probabilities = signal_model.predict_proba(features)
```

#### K-Means Clustering (Market Regimes)
```python
from ml_core.kmeans import KMeans

# Features: returns, volatility, volume
regime_features = np.column_stack([returns, volatility, volume_normalized])

# Identify market regimes
kmeans = KMeans(n_clusters=3, random_state=42)
labels = kmeans.fit_predict(regime_features)

# Analyze regimes
current_regime = labels[-1]
regime_descriptions = ['Low Vol', 'Normal', 'High Vol']

# Find optimal number of clusters
inertias = kmeans.elbow_method(regime_features, max_k=10)
```

### 4. Market Analysis
```python
from crypto_analysis.market_analyzer import CryptoMarketAnalyzer

analyzer = CryptoMarketAnalyzer()

# Comprehensive market analysis
analysis = analyzer.analyze_market_structure(prices, volumes)

print(f"Trend Direction: {analysis['trend_strength']['direction']}")
print(f"Volatility Regime: {analysis['volatility_regime']['regime']}")
print(f"Market Momentum: {analysis['market_momentum']['momentum_score']}")
```

## 🎯 Contoh Analisis Lengkap

```python
import numpy as np
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression

def analyze_crypto(symbol='bitcoin', days=30):
    # 1. Fetch Data
    fetcher = CryptoDataFetcher()
    data = fetcher.fetch_price_history(symbol, days)
    
    prices = data['prices']
    volumes = data['volumes']
    
    # 2. Technical Analysis
    indicators = TechnicalIndicators()
    
    sma_20 = indicators.simple_moving_average(prices, 20)
    rsi = indicators.rsi(prices)
    macd_line, signal_line, histogram = indicators.macd(prices)
    
    # 3. Prepare ML Features
    min_len = min(len(sma_20), len(rsi))
    features = np.column_stack([
        sma_20[-min_len:],
        rsi[-min_len:],
        np.diff(prices[-min_len-1:])
    ])
    
    # Normalize
    features = (features - np.mean(features, axis=0)) / np.std(features, axis=0)
    
    # 4. Price Prediction
    target = np.diff(prices[-min_len:])
    
    price_model = LinearRegression()
    price_model.fit(features[:-1], target)
    
    next_change = price_model.predict(features[-1:])
    predicted_price = prices[-1] + next_change[0]
    
    # 5. Trading Signals
    binary_target = (target > 0).astype(int)
    
    signal_model = LogisticRegression()
    signal_model.fit(features[:-1], binary_target)
    
    current_signal = signal_model.generate_trading_signals(features[-1:])
    signal_text = ['SELL', 'HOLD', 'BUY'][current_signal[0] + 1]
    
    # 6. Results
    return {
        'current_price': prices[-1],
        'predicted_price': predicted_price,
        'rsi': rsi[-1],
        'macd_signal': 'Bullish' if histogram[-1] > 0 else 'Bearish',
        'trading_signal': signal_text
    }

# Usage
results = analyze_crypto('bitcoin', 30)
print(f"Current: ${results['current_price']:.2f}")
print(f"Predicted: ${results['predicted_price']:.2f}")
print(f"Signal: {results['trading_signal']}")
```

## 🔧 Customization

### Menambah Cryptocurrency Baru
```python
# Di data_fetcher.py, tambahkan mapping symbol
symbol_map = {
    'bitcoin': 'BTCUSDT',
    'ethereum': 'ETHUSDT',
    'your_crypto': 'YOURUSDT'  # Tambahkan di sini
}
```

### Membuat Indikator Custom
```python
# Di technical_indicators.py
@staticmethod
def custom_indicator(prices, param1=10, param2=0.5):
    """Custom technical indicator"""
    result = []
    for i in range(param1, len(prices)):
        value = np.mean(prices[i-param1:i]) * param2
        result.append(value)
    return np.array(result)
```

### Menambah Model ML Baru
```python
# Buat file baru: ml_core/neural_network.py
class SimpleNeuralNetwork:
    def __init__(self, hidden_size=10):
        self.hidden_size = hidden_size
        self.weights1 = None
        self.weights2 = None
    
    def _sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def fit(self, X, y):
        # Initialize weights
        input_size = X.shape[1]
        self.weights1 = np.random.normal(0, 0.1, (input_size, self.hidden_size))
        self.weights2 = np.random.normal(0, 0.1, (self.hidden_size, 1))
        
        # Training loop
        for epoch in range(1000):
            # Forward pass
            hidden = self._sigmoid(np.dot(X, self.weights1))
            output = np.dot(hidden, self.weights2)
            
            # Backward pass (simplified)
            error = y.reshape(-1, 1) - output
            
            # Update weights
            self.weights2 += 0.01 * np.dot(hidden.T, error)
            hidden_error = np.dot(error, self.weights2.T) * hidden * (1 - hidden)
            self.weights1 += 0.01 * np.dot(X.T, hidden_error)
    
    def predict(self, X):
        hidden = self._sigmoid(np.dot(X, self.weights1))
        return np.dot(hidden, self.weights2).flatten()
```

## 📈 Tips Penggunaan

### 1. Data Quality
- Gunakan data minimal 30 hari untuk analisis yang akurat
- Periksa missing values dan outliers
- Normalize features sebelum ML training

### 2. Model Training
- Gunakan train/validation/test split (60/20/20)
- Monitor overfitting dengan validation score
- Tune hyperparameters secara sistematis

### 3. Trading Signals
- Kombinasikan multiple indicators
- Set threshold yang konservatif
- Backtest strategy sebelum live trading

### 4. Risk Management
- Jangan rely 100% pada model predictions
- Gunakan stop-loss dan take-profit
- Diversifikasi portfolio

## ⚠️ Disclaimer

Sistem ini untuk tujuan edukasi dan penelitian. Cryptocurrency trading memiliki risiko tinggi. Selalu lakukan riset sendiri dan konsultasi dengan ahli keuangan sebelum membuat keputusan investasi.
