"""
Crypto Analysis Example - Demonstrating the ML system
Pure NumPy implementation for cryptocurrency analysis
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans

def demonstrate_technical_analysis():
    """Demonstrate technical analysis capabilities"""
    print("🔧 Technical Analysis Demonstration")
    print("-" * 40)
    
    # Fetch sample data
    fetcher = CryptoDataFetcher()
    data = fetcher.fetch_price_history('bitcoin', days=30)
    
    prices = data['prices']
    volumes = data['volumes']
    
    # Initialize indicators
    indicators = TechnicalIndicators()
    
    # Calculate various indicators
    print(f"📊 Analyzing {len(prices)} price points...")
    
    # Moving Averages
    sma_20 = indicators.simple_moving_average(prices, 20)
    ema_12 = indicators.exponential_moving_average(prices, 12)
    
    print(f"✅ SMA(20): {sma_20[-1]:.2f}")
    print(f"✅ EMA(12): {ema_12[-1]:.2f}")
    
    # RSI
    rsi = indicators.rsi(prices)
    print(f"✅ RSI: {rsi[-1]:.1f} ({'Overbought' if rsi[-1] > 70 else 'Oversold' if rsi[-1] < 30 else 'Neutral'})")
    
    # MACD
    macd_line, signal_line, histogram = indicators.macd(prices)
    signal = "Bullish" if histogram[-1] > 0 else "Bearish"
    print(f"✅ MACD Signal: {signal}")
    
    # Bollinger Bands
    upper_bb, middle_bb, lower_bb = indicators.bollinger_bands(prices)
    bb_position = (prices[-len(upper_bb):] - lower_bb) / (upper_bb - lower_bb)
    print(f"✅ Bollinger Band Position: {bb_position[-1]:.2f} (0=Lower, 1=Upper)")
    
    return data

def demonstrate_ml_prediction():
    """Demonstrate ML price prediction"""
    print("\n🤖 ML Price Prediction Demonstration")
    print("-" * 40)
    
    # Get data
    fetcher = CryptoDataFetcher()
    data = fetcher.fetch_price_history('bitcoin', days=60)
    
    prices = data['prices']
    
    # Prepare features (simple example)
    indicators = TechnicalIndicators()
    
    # Create features from technical indicators
    sma_5 = indicators.simple_moving_average(prices, 5)
    sma_20 = indicators.simple_moving_average(prices, 20)
    rsi = indicators.rsi(prices)
    
    # Align features
    min_len = min(len(sma_5), len(sma_20), len(rsi))
    
    features = np.column_stack([
        sma_5[-min_len:],
        sma_20[-min_len:],
        rsi[-min_len:],
        np.diff(prices[-min_len-1:])  # Price changes
    ])
    
    # Normalize features
    features = (features - np.mean(features, axis=0)) / np.std(features, axis=0)
    
    # Target: next period price change
    target = np.diff(prices[-min_len-1:])
    target = target[:min_len]  # Ensure same length as features

    # Split data
    split_idx = int(len(features) * 0.8)
    X_train, X_test = features[:split_idx], features[split_idx:]
    y_train, y_test = target[:split_idx], target[split_idx:]
    
    # Train Linear Regression
    print("📈 Training Linear Regression model...")
    lr_model = LinearRegression(learning_rate=0.01, max_iterations=1000)
    lr_model.fit(X_train, y_train)
    
    # Make predictions
    predictions = lr_model.predict(X_test)
    score = lr_model.score(X_test, y_test)
    
    print(f"✅ Model R² Score: {score:.3f}")
    print(f"✅ Last 3 Predictions: {predictions[-3:]}")
    print(f"✅ Last 3 Actual: {y_test[-3:]}")
    
    return lr_model, X_test, y_test

def demonstrate_trading_signals():
    """Demonstrate trading signal generation"""
    print("\n🎯 Trading Signal Generation Demonstration")
    print("-" * 40)
    
    # Get data
    fetcher = CryptoDataFetcher()
    data = fetcher.fetch_price_history('bitcoin', days=30)
    
    prices = data['prices']
    volumes = data['volumes']
    
    # Prepare features
    indicators = TechnicalIndicators()
    
    sma_short = indicators.simple_moving_average(prices, 5)
    sma_long = indicators.simple_moving_average(prices, 20)
    rsi = indicators.rsi(prices)
    volume_ratio = volumes[20:] / indicators.simple_moving_average(volumes, 20)
    
    # Align features
    min_len = min(len(sma_short), len(sma_long), len(rsi), len(volume_ratio))
    
    features = np.column_stack([
        sma_short[-min_len:],
        sma_long[-min_len:],
        rsi[-min_len:],
        volume_ratio[-min_len:]
    ])
    
    # Normalize
    features = (features - np.mean(features, axis=0)) / np.std(features, axis=0)
    
    # Create binary target (1 for price increase, 0 for decrease)
    price_changes = np.diff(prices[-min_len-1:])
    target = (price_changes[:min_len] > 0).astype(int)
    
    # Split data
    split_idx = int(len(features) * 0.8)
    X_train, X_test = features[:split_idx], features[split_idx:]
    y_train, y_test = target[:split_idx], target[split_idx:]
    
    # Train Logistic Regression
    print("🎯 Training Logistic Regression for signals...")
    lr_model = LogisticRegression(learning_rate=0.1, max_iterations=1000)
    lr_model.fit(X_train, y_train)
    
    # Generate trading signals
    signals = lr_model.generate_trading_signals(X_test)
    accuracy = lr_model.score(X_test, y_test)
    
    print(f"✅ Signal Accuracy: {accuracy:.3f}")
    
    # Count signals
    buy_signals = np.sum(signals == 1)
    sell_signals = np.sum(signals == -1)
    hold_signals = np.sum(signals == 0)
    
    print(f"✅ Buy Signals: {buy_signals}")
    print(f"✅ Sell Signals: {sell_signals}")
    print(f"✅ Hold Signals: {hold_signals}")
    
    return lr_model, signals

def demonstrate_market_regimes():
    """Demonstrate market regime identification"""
    print("\n🔄 Market Regime Identification Demonstration")
    print("-" * 40)
    
    # Get data
    fetcher = CryptoDataFetcher()
    data = fetcher.fetch_price_history('bitcoin', days=60)
    
    prices = data['prices']
    volumes = data['volumes']
    
    # Calculate features for regime identification
    returns = np.diff(np.log(prices))
    volatility = np.array([np.std(returns[max(0, i-24):i+1]) for i in range(len(returns))])
    volume_normalized = (volumes[1:] - np.mean(volumes)) / np.std(volumes)
    
    # Align arrays
    min_len = min(len(returns), len(volatility), len(volume_normalized))
    
    features = np.column_stack([
        returns[-min_len:],
        volatility[-min_len:],
        volume_normalized[-min_len:]
    ])
    
    # Normalize features
    features = (features - np.mean(features, axis=0)) / np.std(features, axis=0)
    
    # Apply K-means clustering
    print("🔄 Identifying market regimes with K-means...")
    kmeans = KMeans(n_clusters=3, random_state=42)
    labels = kmeans.fit_predict(features)
    
    # Analyze regimes
    regime_names = ['Low Volatility', 'Normal Market', 'High Volatility']
    
    print(f"✅ Identified {kmeans.n_clusters} market regimes")
    
    for i in range(kmeans.n_clusters):
        regime_points = np.sum(labels == i)
        percentage = (regime_points / len(labels)) * 100
        print(f"✅ Regime {i} ({regime_names[i]}): {regime_points} points ({percentage:.1f}%)")
    
    current_regime = labels[-1]
    print(f"✅ Current Market Regime: {current_regime} ({regime_names[current_regime]})")
    
    return kmeans, labels

def main():
    """Run all demonstrations"""
    print("🚀 Crypto ML Analysis System - Examples")
    print("=" * 50)
    
    try:
        # Technical Analysis
        data = demonstrate_technical_analysis()
        
        # ML Prediction
        model, X_test, y_test = demonstrate_ml_prediction()
        
        # Trading Signals
        signal_model, signals = demonstrate_trading_signals()
        
        # Market Regimes
        regime_model, labels = demonstrate_market_regimes()
        
        print("\n🎉 All demonstrations completed successfully!")
        print("\n📚 What you can do with this system:")
        print("   • Fetch real crypto data from APIs")
        print("   • Calculate technical indicators (RSI, MACD, etc.)")
        print("   • Predict price movements with ML")
        print("   • Generate buy/sell trading signals")
        print("   • Identify market regimes and patterns")
        print("   • All using pure NumPy - no external ML frameworks!")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        print("This might be due to network issues or API limits.")
        print("The system will work with real data when APIs are accessible.")

if __name__ == "__main__":
    main()
