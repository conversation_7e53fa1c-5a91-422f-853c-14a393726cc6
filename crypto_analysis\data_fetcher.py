"""
Crypto Data Fetcher - Pure Python Implementation
Fetch cryptocurrency market data from public APIs
"""
import requests
import json
import numpy as np
from datetime import datetime, timedelta

class CryptoDataFetcher:
    """Fetch cryptocurrency data from various sources"""
    
    def __init__(self):
        self.base_urls = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'binance': 'https://api.binance.com/api/v3'
        }
    
    def fetch_price_history(self, symbol='bitcoin', days=30, source='coingecko'):
        """
        Fetch historical price data
        Args:
            symbol: Cryptocurrency symbol
            days: Number of days of history
            source: Data source ('coingecko' or 'binance')
        Returns:
            Dictionary with timestamps, prices, volumes
        """
        if source == 'coingecko':
            return self._fetch_coingecko_data(symbol, days)
        elif source == 'binance':
            return self._fetch_binance_data(symbol, days)
        else:
            raise ValueError("Unsupported data source")
    
    def _fetch_coingecko_data(self, symbol, days):
        """Fetch data from CoinGecko API"""
        url = f"{self.base_urls['coingecko']}/coins/{symbol}/market_chart"
        params = {
            'vs_currency': 'usd',
            'days': days,
            'interval': 'hourly' if days <= 90 else 'daily'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Extract data
            prices = np.array([point[1] for point in data['prices']])
            volumes = np.array([point[1] for point in data['total_volumes']])
            timestamps = np.array([point[0] for point in data['prices']])
            
            return {
                'timestamps': timestamps,
                'prices': prices,
                'volumes': volumes,
                'symbol': symbol
            }
        
        except requests.RequestException as e:
            print(f"Error fetching data from CoinGecko: {e}")
            return self._generate_synthetic_data(symbol, days)
    
    def _fetch_binance_data(self, symbol, days):
        """Fetch data from Binance API"""
        # Convert symbol format (bitcoin -> BTCUSDT)
        symbol_map = {
            'bitcoin': 'BTCUSDT',
            'ethereum': 'ETHUSDT',
            'cardano': 'ADAUSDT',
            'solana': 'SOLUSDT'
        }
        
        binance_symbol = symbol_map.get(symbol, 'BTCUSDT')
        
        url = f"{self.base_urls['binance']}/klines"
        params = {
            'symbol': binance_symbol,
            'interval': '1h',
            'limit': min(days * 24, 1000)  # Max 1000 data points
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Extract OHLCV data
            timestamps = np.array([float(kline[0]) for kline in data])
            opens = np.array([float(kline[1]) for kline in data])
            highs = np.array([float(kline[2]) for kline in data])
            lows = np.array([float(kline[3]) for kline in data])
            closes = np.array([float(kline[4]) for kline in data])
            volumes = np.array([float(kline[5]) for kline in data])
            
            return {
                'timestamps': timestamps,
                'opens': opens,
                'highs': highs,
                'lows': lows,
                'closes': closes,
                'prices': closes,  # Use closing prices as main price
                'volumes': volumes,
                'symbol': symbol
            }
        
        except requests.RequestException as e:
            print(f"Error fetching data from Binance: {e}")
            return self._generate_synthetic_data(symbol, days)
    
    def _generate_synthetic_data(self, symbol, days):
        """Generate synthetic crypto data for testing"""
        print(f"Generating synthetic data for {symbol}")
        
        # Generate timestamps
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        timestamps = np.linspace(
            start_time.timestamp() * 1000,
            end_time.timestamp() * 1000,
            days * 24
        )
        
        # Generate realistic crypto price movement
        np.random.seed(42)  # For reproducible results
        
        # Base price
        base_price = 50000 if symbol == 'bitcoin' else 3000
        
        # Generate price with trend and volatility
        returns = np.random.normal(0.0001, 0.02, len(timestamps))  # Small positive trend, high volatility
        prices = np.zeros(len(timestamps))
        prices[0] = base_price
        
        for i in range(1, len(prices)):
            prices[i] = prices[i-1] * (1 + returns[i])
        
        # Generate volumes (correlated with price volatility)
        volume_base = 1000000
        volatility = np.abs(returns)
        volumes = volume_base * (1 + volatility * 10) * np.random.uniform(0.5, 2.0, len(timestamps))
        
        # Generate OHLC data
        highs = prices * np.random.uniform(1.0, 1.05, len(prices))
        lows = prices * np.random.uniform(0.95, 1.0, len(prices))
        opens = np.roll(prices, 1)
        opens[0] = prices[0]
        
        return {
            'timestamps': timestamps,
            'opens': opens,
            'highs': highs,
            'lows': lows,
            'closes': prices,
            'prices': prices,
            'volumes': volumes,
            'symbol': symbol,
            'synthetic': True
        }
    
    def get_multiple_symbols(self, symbols, days=30):
        """Fetch data for multiple cryptocurrencies"""
        data = {}
        for symbol in symbols:
            print(f"Fetching data for {symbol}...")
            data[symbol] = self.fetch_price_history(symbol, days)
        return data
    
    def calculate_returns(self, prices):
        """Calculate price returns"""
        return np.diff(np.log(prices))
    
    def calculate_volatility(self, returns, window=24):
        """Calculate rolling volatility"""
        volatility = np.zeros(len(returns))
        for i in range(window, len(returns)):
            volatility[i] = np.std(returns[i-window:i])
        return volatility
