"""
Crypto ML Analysis System - Main Entry Point
Pure NumPy implementation for cryptocurrency market analysis
"""
import numpy as np
import matplotlib.pyplot as plt
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from crypto_analysis.market_analyzer import CryptoMarketAnalyzer
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans
from ml_core.matrix_ops import MatrixOps

class CryptoMLSystem:
    """Main crypto ML analysis system"""
    
    def __init__(self):
        self.data_fetcher = CryptoDataFetcher()
        self.indicators = TechnicalIndicators()
        self.analyzer = CryptoMarketAnalyzer()
        self.matrix_ops = MatrixOps()
        
    def run_complete_analysis(self, symbol='bitcoin', days=30):
        """Run complete crypto analysis pipeline"""
        print(f"🚀 Starting Crypto ML Analysis for {symbol.upper()}")
        print("=" * 50)
        
        # 1. Fetch Data
        print("📊 Fetching market data...")
        data = self.data_fetcher.fetch_price_history(symbol, days)
        
        if 'synthetic' in data:
            print("⚠️  Using synthetic data for demonstration")
        
        prices = data['prices']
        volumes = data['volumes']
        
        print(f"✅ Loaded {len(prices)} data points")
        
        # 2. Technical Analysis
        print("\n📈 Calculating technical indicators...")
        tech_analysis = self._calculate_technical_indicators(data)
        
        # 3. Market Structure Analysis
        print("🔍 Analyzing market structure...")
        market_analysis = self.analyzer.analyze_market_structure(prices, volumes)
        
        # 4. ML Predictions
        print("🤖 Running ML predictions...")
        ml_results = self._run_ml_analysis(data)
        
        # 5. Generate Report
        print("\n📋 Generating analysis report...")
        self._generate_report(symbol, tech_analysis, market_analysis, ml_results)
        
        return {
            'data': data,
            'technical_analysis': tech_analysis,
            'market_analysis': market_analysis,
            'ml_results': ml_results
        }
    
    def _calculate_technical_indicators(self, data):
        """Calculate all technical indicators"""
        prices = data['prices']
        volumes = data['volumes']
        
        # Get OHLC data if available
        if 'highs' in data:
            highs = data['highs']
            lows = data['lows']
            closes = data['closes']
        else:
            # Approximate OHLC from prices
            highs = prices * 1.02
            lows = prices * 0.98
            closes = prices
        
        indicators = {}
        
        try:
            indicators['sma_20'] = self.indicators.simple_moving_average(prices, 20)
            indicators['ema_12'] = self.indicators.exponential_moving_average(prices, 12)
            indicators['rsi'] = self.indicators.rsi(prices)
            
            macd_line, signal_line, histogram = self.indicators.macd(prices)
            indicators['macd'] = {
                'line': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
            
            upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
            indicators['bollinger_bands'] = {
                'upper': upper_bb,
                'middle': middle_bb,
                'lower': lower_bb
            }
            
            indicators['obv'] = self.indicators.obv(closes, volumes)
            
            if len(highs) > 14:
                indicators['atr'] = self.indicators.atr(highs, lows, closes)
            
        except Exception as e:
            print(f"⚠️  Error calculating some indicators: {e}")
        
        return indicators
    
    def _run_ml_analysis(self, data):
        """Run machine learning analysis"""
        prices = data['prices']
        volumes = data['volumes']
        
        # Prepare features
        features = self._prepare_ml_features(data)
        
        if features is None or len(features) < 20:
            print("⚠️  Insufficient data for ML analysis")
            return {}
        
        results = {}
        
        try:
            # 1. Price Prediction (Linear Regression)
            print("  📊 Training price prediction model...")
            price_results = self._predict_prices(features, prices)
            results['price_prediction'] = price_results
            
            # 2. Trading Signals (Logistic Regression)
            print("  🎯 Training trading signal model...")
            signal_results = self._generate_trading_signals(features, prices)
            results['trading_signals'] = signal_results
            
            # 3. Market Regime Clustering
            print("  🔄 Identifying market regimes...")
            regime_results = self._identify_market_regimes(features)
            results['market_regimes'] = regime_results
            
        except Exception as e:
            print(f"⚠️  Error in ML analysis: {e}")
        
        return results
    
    def _prepare_ml_features(self, data):
        """Prepare features for ML models"""
        prices = data['prices']
        volumes = data['volumes']
        
        if len(prices) < 50:
            return None
        
        # Calculate returns
        returns = np.diff(np.log(prices))
        
        # Technical indicators as features
        try:
            sma_5 = self.indicators.simple_moving_average(prices, 5)
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            rsi = self.indicators.rsi(prices)
            
            # Volume indicators
            volume_sma = self.indicators.simple_moving_average(volumes, 10)
            
            # Align all features to same length
            min_len = min(len(sma_5), len(sma_20), len(rsi), len(volume_sma))
            
            if min_len < 10:
                return None
            
            features = np.column_stack([
                sma_5[-min_len:],
                sma_20[-min_len:],
                rsi[-min_len:],
                volume_sma[-min_len:],
                returns[-min_len:] if len(returns) >= min_len else np.zeros(min_len)
            ])
            
            # Normalize features
            features_norm, _, _ = self.matrix_ops.normalize(features)
            
            return features_norm
            
        except Exception as e:
            print(f"Error preparing features: {e}")
            return None
    
    def _predict_prices(self, features, prices):
        """Predict future prices using linear regression"""
        if len(features) < 20:
            return {}
        
        # Prepare target (next period price change)
        target = np.diff(prices[-len(features)-1:])
        
        if len(target) != len(features):
            target = target[:len(features)]
        
        # Split data
        split_idx = int(len(features) * 0.8)
        X_train, X_test = features[:split_idx], features[split_idx:]
        y_train, y_test = target[:split_idx], target[split_idx:]
        
        # Train model
        model = LinearRegression(learning_rate=0.01, max_iterations=1000)
        model.fit(X_train, y_train)
        
        # Make predictions
        train_pred = model.predict(X_train)
        test_pred = model.predict(X_test)
        
        # Calculate metrics
        train_score = model.score(X_train, y_train)
        test_score = model.score(X_test, y_test)
        
        return {
            'model': model,
            'train_score': train_score,
            'test_score': test_score,
            'predictions': test_pred,
            'actual': y_test
        }
    
    def _generate_trading_signals(self, features, prices):
        """Generate buy/sell signals using logistic regression"""
        if len(features) < 20:
            return {}
        
        # Create binary target (1 for price increase, 0 for decrease)
        returns = np.diff(prices[-len(features)-1:])
        target = (returns > 0).astype(int)
        
        if len(target) != len(features):
            target = target[:len(features)]
        
        # Split data
        split_idx = int(len(features) * 0.8)
        X_train, X_test = features[:split_idx], features[split_idx:]
        y_train, y_test = target[:split_idx], target[split_idx:]
        
        # Train model
        model = LogisticRegression(learning_rate=0.1, max_iterations=1000)
        model.fit(X_train, y_train)
        
        # Generate signals
        signals = model.generate_trading_signals(X_test)
        accuracy = model.score(X_test, y_test)
        
        return {
            'model': model,
            'accuracy': accuracy,
            'signals': signals,
            'probabilities': model.predict_proba(X_test)
        }
    
    def _identify_market_regimes(self, features):
        """Identify market regimes using K-means clustering"""
        if len(features) < 10:
            return {}
        
        # Use K-means to identify 3 market regimes
        kmeans = KMeans(n_clusters=3, random_state=42)
        labels = kmeans.fit_predict(features)
        
        return {
            'model': kmeans,
            'labels': labels,
            'centroids': kmeans.centroids,
            'current_regime': labels[-1]
        }
    
    def _generate_report(self, symbol, tech_analysis, market_analysis, ml_results):
        """Generate comprehensive analysis report"""
        print(f"\n🎯 CRYPTO ANALYSIS REPORT - {symbol.upper()}")
        print("=" * 60)
        
        # Market Structure
        trend = market_analysis.get('trend_strength', {})
        print(f"📈 TREND ANALYSIS:")
        print(f"   Direction: {trend.get('direction', 'Unknown').upper()}")
        print(f"   Strength: {trend.get('overall_strength', 0):.2f}")
        
        # Volatility
        vol_regime = market_analysis.get('volatility_regime', {})
        print(f"\n📊 VOLATILITY:")
        print(f"   Regime: {vol_regime.get('regime', 'Unknown').replace('_', ' ').title()}")
        print(f"   Current: {vol_regime.get('current_volatility', 0):.4f}")
        
        # Technical Indicators
        if 'rsi' in tech_analysis and len(tech_analysis['rsi']) > 0:
            rsi_current = tech_analysis['rsi'][-1]
            print(f"\n📋 TECHNICAL INDICATORS:")
            print(f"   RSI: {rsi_current:.1f} ({'Overbought' if rsi_current > 70 else 'Oversold' if rsi_current < 30 else 'Neutral'})")
        
        # ML Results
        if ml_results:
            print(f"\n🤖 ML ANALYSIS:")
            
            if 'price_prediction' in ml_results:
                pred_score = ml_results['price_prediction'].get('test_score', 0)
                print(f"   Price Model R²: {pred_score:.3f}")
            
            if 'trading_signals' in ml_results:
                signal_acc = ml_results['trading_signals'].get('accuracy', 0)
                print(f"   Signal Accuracy: {signal_acc:.3f}")
            
            if 'market_regimes' in ml_results:
                current_regime = ml_results['market_regimes'].get('current_regime', 0)
                print(f"   Current Market Regime: {current_regime}")
        
        print("\n" + "=" * 60)
        print("✅ Analysis Complete!")

def main():
    """Main function"""
    system = CryptoMLSystem()
    
    # Run analysis for Bitcoin
    results = system.run_complete_analysis('bitcoin', days=30)
    
    print(f"\n🎉 Crypto ML Analysis System Ready!")
    print("📚 Available components:")
    print("   - Technical Indicators (RSI, MACD, Bollinger Bands, etc.)")
    print("   - Market Structure Analysis")
    print("   - ML Price Prediction")
    print("   - Trading Signal Generation")
    print("   - Market Regime Identification")

if __name__ == "__main__":
    main()
