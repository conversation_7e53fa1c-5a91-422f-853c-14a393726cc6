"""
Linear Regression - Pure NumPy Implementation
For crypto price trend analysis and prediction
"""
import numpy as np
from .matrix_ops import MatrixOps

class LinearRegression:
    """Linear Regression implementation using NumPy"""
    
    def __init__(self, learning_rate=0.01, max_iterations=1000, tolerance=1e-6):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.weights = None
        self.bias = None
        self.cost_history = []
        
    def fit(self, X, y, method='gradient_descent'):
        """
        Train the linear regression model
        Args:
            X: Feature matrix (n_samples, n_features)
            y: Target values (n_samples,)
            method: 'gradient_descent' or 'normal_equation'
        """
        n_samples, n_features = X.shape
        
        # Initialize weights and bias
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        
        if method == 'gradient_descent':
            self._gradient_descent(X, y)
        elif method == 'normal_equation':
            self._normal_equation(X, y)
    
    def _gradient_descent(self, X, y):
        """Gradient descent optimization"""
        n_samples = X.shape[0]
        
        for i in range(self.max_iterations):
            # Forward pass
            y_pred = self.predict(X)
            
            # Calculate cost
            cost = np.mean((y_pred - y) ** 2)
            self.cost_history.append(cost)
            
            # Calculate gradients
            dw = (2/n_samples) * np.dot(X.T, (y_pred - y))
            db = (2/n_samples) * np.sum(y_pred - y)
            
            # Update parameters
            self.weights -= self.learning_rate * dw
            self.bias -= self.learning_rate * db
            
            # Check for convergence
            if i > 0 and abs(self.cost_history[-2] - self.cost_history[-1]) < self.tolerance:
                break
    
    def _normal_equation(self, X, y):
        """Normal equation solution"""
        X_with_bias = MatrixOps.add_bias(X)
        
        # Calculate (X^T * X)^-1 * X^T * y
        XTX = np.dot(X_with_bias.T, X_with_bias)
        XTX_inv = np.linalg.pinv(XTX)  # Use pseudo-inverse for stability
        XTy = np.dot(X_with_bias.T, y)
        
        params = np.dot(XTX_inv, XTy)
        
        self.bias = params[0]
        self.weights = params[1:]
    
    def predict(self, X):
        """Make predictions"""
        return np.dot(X, self.weights) + self.bias
    
    def score(self, X, y):
        """Calculate R-squared score"""
        y_pred = self.predict(X)
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        return 1 - (ss_res / ss_tot)
    
    def predict_trend(self, X, periods=1):
        """
        Predict future trend for crypto prices
        Args:
            X: Current features
            periods: Number of periods to predict ahead
        """
        predictions = []
        current_X = X[-1:].copy()  # Last observation
        
        for _ in range(periods):
            pred = self.predict(current_X)[0]
            predictions.append(pred)
            
            # Update features for next prediction (simple approach)
            # In practice, you'd update with new technical indicators
            current_X = np.roll(current_X, -1)
            current_X[0, -1] = pred
        
        return np.array(predictions)
    
    def get_feature_importance(self):
        """Get feature importance based on weights magnitude"""
        if self.weights is None:
            return None
        return np.abs(self.weights) / np.sum(np.abs(self.weights))
