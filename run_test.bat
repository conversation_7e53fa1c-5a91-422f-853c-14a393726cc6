@echo off
echo 🧪 Running Crypto ML System Test...
echo ==================================

REM Set Python path
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

REM Check if Python exists
if not exist "%PYTHON_PATH%" (
    echo ❌ Python not found at %PYTHON_PATH%
    echo Please check your Python installation
    pause
    exit /b 1
)

REM Run the test
echo 📊 Starting system test with Python at: %PYTHON_PATH%
echo.
"%PYTHON_PATH%" test_system.py

echo.
echo ✅ Test completed!
pause
