# Setup Guide - Crypto ML System

## 🔧 Mengatasi Masalah Python Path

### Masalah yang Umum Terjadi
```
Python was not found; run without arguments to install from the Microsoft Store
```

### 💡 Solusi yang Tersedia

#### Opsi 1: Gunakan Batch Files (Termudah)
<PERSON>a telah membuat batch files untuk memudahkan eksekusi:

```bash
# Jalankan demo
.\run_demo.bat

# Jalankan analisis lengkap
.\run_main.bat

# Jalankan test sistem
.\run_test.bat
```

#### Opsi 2: Gunakan Path Lengkap Python
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe demo.py
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe main.py
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe test_system.py
```

#### Opsi 3: Tambahkan Python ke System PATH (Permanen)

1. **Buka System Properties:**
   - <PERSON><PERSON> `Win + R`, ketik `sysdm.cpl`
   - Klik "Environment Variables"

2. **Edit PATH Variable:**
   - Di "System Variables", cari "Path"
   - Klik "Edit" → "New"
   - Tambahkan: `C:\Users\<USER>\AppData\Local\Programs\Python\Python313`
   - Tambahkan: `C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts`

3. **Restart Command Prompt/PowerShell**

4. **Test:**
   ```bash
   python --version
   ```

#### Opsi 4: Gunakan Python Launcher (py)
```bash
py demo.py
py main.py
py test_system.py
```

## 🚀 Quick Start Commands

### Menggunakan Batch Files (Recommended)
```bash
# Demo sistem
.\run_demo.bat

# Analisis lengkap Bitcoin
.\run_main.bat

# Test semua komponen
.\run_test.bat
```

### Menggunakan Path Lengkap
```bash
# Demo
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe demo.py

# Main analysis
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe main.py

# Test
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe test_system.py
```

## 📦 Instalasi Dependencies

### Opsi 1: Menggunakan Batch File
```bash
.\install_requirements.bat
```

### Opsi 2: Manual
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install numpy requests matplotlib
```

## 🧪 Verifikasi Instalasi

### Test Python dan NumPy
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -c "import numpy as np; print('NumPy version:', np.__version__); print('✅ NumPy working!')"
```

### Test Sistem ML
```bash
.\run_test.bat
```

## 📁 Struktur File

Pastikan struktur file Anda seperti ini:
```
ML/
├── ml_core/
│   ├── __init__.py
│   ├── matrix_ops.py
│   ├── linear_regression.py
│   ├── logistic_regression.py
│   └── kmeans.py
├── crypto_analysis/
│   ├── __init__.py
│   ├── data_fetcher.py
│   ├── technical_indicators.py
│   └── market_analyzer.py
├── examples/
│   └── crypto_analysis_example.py
├── main.py
├── demo.py
├── test_system.py
├── run_demo.bat          # ← Batch files untuk eksekusi mudah
├── run_main.bat
├── run_test.bat
├── requirements.txt
├── README.md
├── USAGE_GUIDE.md
└── SETUP_GUIDE.md
```

## 🔍 Troubleshooting

### Error: "ModuleNotFoundError: No module named 'numpy'"
```bash
# Install numpy
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install numpy
```

### Error: "ModuleNotFoundError: No module named 'requests'"
```bash
# Install requests
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install requests
```

### Error: "Permission denied"
- Jalankan Command Prompt/PowerShell sebagai Administrator
- Atau gunakan `--user` flag:
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install --user numpy requests matplotlib
```

### Error: "Python path not found"
- Periksa apakah Python terinstall di lokasi yang benar
- Gunakan `where python` atau `Get-Command python` untuk mencari lokasi Python

## 🎯 Rekomendasi

1. **Gunakan Batch Files** - Paling mudah dan tidak perlu setup tambahan
2. **Tambahkan ke PATH** - Jika ingin menggunakan `python` command secara global
3. **Gunakan Virtual Environment** - Untuk project development yang lebih kompleks

## 📞 Support

Jika masih ada masalah:
1. Pastikan Python 3.13 terinstall dengan benar
2. Cek lokasi instalasi Python
3. Gunakan batch files yang sudah disediakan
4. Atau gunakan path lengkap Python

## ✅ Verifikasi Final

Setelah setup, jalankan:
```bash
.\run_test.bat
```

Jika muncul output seperti ini, sistem sudah siap:
```
🧪 Testing Crypto ML System...
1. Testing Data Fetcher...
   ✅ Generated 720 price points
2. Testing Technical Indicators...
   ✅ SMA(10): 39694.23
   ✅ RSI: 49.6
3. Testing Linear Regression...
   ✅ Model trained successfully
   ✅ Sample predictions: [394.29 405.14 440.71]

🎉 All tests passed! System is working correctly.
```
