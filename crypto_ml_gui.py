"""
Crypto ML Bloomberg Terminal
Professional cryptocurrency analysis terminal with Bloomberg-style interface
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, font
import threading
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
import time
import random

# Import our ML components
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from crypto_analysis.market_analyzer import CryptoMarketAnalyzer
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans
from ml_core.matrix_ops import MatrixOps

class CryptoBloombergTerminal:
    """Bloomberg-style Cryptocurrency Analysis Terminal"""

    def __init__(self, root):
        self.root = root
        self.root.title("CRYPTO BLOOMBERG TERMINAL - Professional Market Analysis")
        self.root.geometry("1920x1080")
        self.root.configure(bg='#000000')
        self.root.state('zoomed')  # Maximize window
        
        # Initialize ML components
        self.data_fetcher = CryptoDataFetcher()
        self.indicators = TechnicalIndicators()
        self.analyzer = CryptoMarketAnalyzer()
        self.matrix_ops = MatrixOps()

        # Data storage
        self.current_data = None
        self.current_symbol = None
        self.market_data = {}
        self.real_time_data = {}

        # Bloomberg-style colors
        self.colors = {
            'bg_primary': '#000000',
            'bg_secondary': '#1a1a1a',
            'bg_panel': '#2d2d2d',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'accent_orange': '#ff6600',
            'accent_blue': '#0066cc',
            'accent_green': '#00cc66',
            'accent_red': '#cc0066',
            'grid_color': '#333333'
        }

        # Setup GUI
        self.setup_bloomberg_styles()
        self.create_bloomberg_interface()
        self.start_real_time_updates()
        
    def setup_bloomberg_styles(self):
        """Setup Bloomberg Terminal-style themes"""
        style = ttk.Style()
        style.theme_use('clam')

        # Bloomberg-style fonts
        self.fonts = {
            'title': ('Segoe UI', 14, 'bold'),
            'header': ('Segoe UI', 11, 'bold'),
            'body': ('Segoe UI', 9),
            'mono': ('Consolas', 9),
            'large_mono': ('Consolas', 11, 'bold')
        }

        # Configure Bloomberg-style themes
        style.configure('Bloomberg.TLabel',
                       font=self.fonts['title'],
                       background=self.colors['bg_primary'],
                       foreground=self.colors['accent_orange'])

        style.configure('Panel.TLabel',
                       font=self.fonts['header'],
                       background=self.colors['bg_panel'],
                       foreground=self.colors['text_primary'])

        style.configure('Data.TLabel',
                       font=self.fonts['mono'],
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'])

        style.configure('Bloomberg.TButton',
                       font=self.fonts['body'],
                       padding=8)

        style.configure('Bloomberg.TFrame',
                       background=self.colors['bg_panel'],
                       relief='solid',
                       borderwidth=1)
        
    def create_bloomberg_interface(self):
        """Create Bloomberg Terminal-style interface"""
        # Main container with black background
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Top header bar
        self.create_header_bar(main_frame)

        # Main content area with multiple panels
        content_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # Left panel - Market data and watchlist
        left_panel = tk.Frame(content_frame, bg=self.colors['bg_panel'], width=300)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 2))
        left_panel.pack_propagate(False)

        # Center panel - Main charts and analysis
        center_panel = tk.Frame(content_frame, bg=self.colors['bg_panel'])
        center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2)

        # Right panel - News and analytics
        right_panel = tk.Frame(content_frame, bg=self.colors['bg_panel'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(2, 0))
        right_panel.pack_propagate(False)

        # Bottom status bar
        self.create_status_bar(main_frame)

        # Populate panels
        self.create_market_panel(left_panel)
        self.create_main_chart_panel(center_panel)
        self.create_analytics_panel(right_panel)

        # Initialize with Bitcoin data
        self.current_symbol = 'bitcoin'
        self.load_initial_data()

    def create_header_bar(self, parent):
        """Create Bloomberg-style header bar"""
        header = tk.Frame(parent, bg=self.colors['accent_orange'], height=40)
        header.pack(fill=tk.X)
        header.pack_propagate(False)

        # Logo and title
        title_frame = tk.Frame(header, bg=self.colors['accent_orange'])
        title_frame.pack(side=tk.LEFT, padx=10, pady=5)

        tk.Label(title_frame, text="CRYPTO TERMINAL",
                font=self.fonts['title'], bg=self.colors['accent_orange'],
                fg='white').pack(side=tk.LEFT)

        # Real-time clock
        self.clock_var = tk.StringVar()
        clock_label = tk.Label(header, textvariable=self.clock_var,
                              font=self.fonts['body'], bg=self.colors['accent_orange'],
                              fg='white')
        clock_label.pack(side=tk.RIGHT, padx=10, pady=5)

        # Market status
        self.market_status_var = tk.StringVar()
        self.market_status_var.set("MARKET: OPEN")
        status_label = tk.Label(header, textvariable=self.market_status_var,
                               font=self.fonts['body'], bg=self.colors['accent_orange'],
                               fg='white')
        status_label.pack(side=tk.RIGHT, padx=20, pady=5)

    def create_status_bar(self, parent):
        """Create bottom status bar"""
        self.status_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=25)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_frame.pack_propagate(False)

        self.status_var = tk.StringVar()
        self.status_var.set("READY - Bloomberg Terminal Initialized")
        status_label = tk.Label(self.status_frame, textvariable=self.status_var,
                               font=self.fonts['body'], bg=self.colors['bg_secondary'],
                               fg=self.colors['text_secondary'])
        status_label.pack(side=tk.LEFT, padx=10, pady=2)

    def create_market_panel(self, parent):
        """Create left market data panel"""
        # Panel title
        title_frame = tk.Frame(parent, bg=self.colors['accent_blue'], height=30)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="MARKET DATA", font=self.fonts['header'],
                bg=self.colors['accent_blue'], fg='white').pack(pady=5)

        # Watchlist
        watchlist_frame = tk.Frame(parent, bg=self.colors['bg_panel'])
        watchlist_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrollable watchlist
        canvas = tk.Canvas(watchlist_frame, bg=self.colors['bg_panel'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(watchlist_frame, orient="vertical", command=canvas.yview)
        self.watchlist_frame = tk.Frame(canvas, bg=self.colors['bg_panel'])

        self.watchlist_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.watchlist_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add crypto symbols to watchlist
        self.create_watchlist()

    def create_watchlist(self):
        """Create cryptocurrency watchlist"""
        cryptos = [
            ('BTC', 'Bitcoin', 45000, 2.5),
            ('ETH', 'Ethereum', 3200, 1.8),
            ('ADA', 'Cardano', 0.45, -0.5),
            ('SOL', 'Solana', 95, 3.2),
            ('DOT', 'Polkadot', 6.8, -1.2),
            ('LINK', 'Chainlink', 14.5, 0.8),
            ('MATIC', 'Polygon', 0.85, 4.1),
            ('AVAX', 'Avalanche', 18.2, -2.1)
        ]

        for i, (symbol, name, price, change) in enumerate(cryptos):
            self.create_watchlist_item(symbol, name, price, change, i)

    def create_watchlist_item(self, symbol, name, price, change, row):
        """Create individual watchlist item"""
        item_frame = tk.Frame(self.watchlist_frame, bg=self.colors['bg_secondary'],
                             relief='solid', borderwidth=1)
        item_frame.pack(fill=tk.X, padx=2, pady=1)

        # Symbol
        tk.Label(item_frame, text=symbol, font=self.fonts['large_mono'],
                bg=self.colors['bg_secondary'], fg=self.colors['accent_orange']).pack(anchor='w', padx=5)

        # Name
        tk.Label(item_frame, text=name, font=self.fonts['body'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_secondary']).pack(anchor='w', padx=5)

        # Price and change
        price_frame = tk.Frame(item_frame, bg=self.colors['bg_secondary'])
        price_frame.pack(fill=tk.X, padx=5, pady=2)

        tk.Label(price_frame, text=f"${price:,.2f}", font=self.fonts['mono'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(side='left')

        change_color = self.colors['accent_green'] if change > 0 else self.colors['accent_red']
        change_text = f"+{change:.1f}%" if change > 0 else f"{change:.1f}%"
        tk.Label(price_frame, text=change_text, font=self.fonts['mono'],
                bg=self.colors['bg_secondary'], fg=change_color).pack(side='right')

        # Click binding
        item_frame.bind("<Button-1>", lambda e, s=symbol.lower(): self.select_crypto(s))

    def create_main_chart_panel(self, parent):
        """Create main chart panel"""
        # Panel title
        title_frame = tk.Frame(parent, bg=self.colors['accent_blue'], height=30)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="PRICE CHART & TECHNICAL ANALYSIS",
                font=self.fonts['header'], bg=self.colors['accent_blue'],
                fg='white').pack(pady=5)

        # Chart controls
        controls_frame = tk.Frame(parent, bg=self.colors['bg_panel'], height=40)
        controls_frame.pack(fill=tk.X, padx=5, pady=2)
        controls_frame.pack_propagate(False)

        # Time period buttons
        periods = [('1H', '1h'), ('4H', '4h'), ('1D', '1d'), ('1W', '1w'), ('1M', '1m')]
        for text, value in periods:
            btn = tk.Button(controls_frame, text=text, font=self.fonts['body'],
                           bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                           relief='solid', borderwidth=1, padx=10,
                           command=lambda v=value: self.change_timeframe(v))
            btn.pack(side=tk.LEFT, padx=2, pady=5)

        # Analysis buttons
        tk.Button(controls_frame, text="TECHNICAL", font=self.fonts['body'],
                 bg=self.colors['accent_green'], fg='white', relief='solid',
                 borderwidth=1, padx=10, command=self.run_technical_analysis).pack(side=tk.LEFT, padx=10, pady=5)

        tk.Button(controls_frame, text="ML PREDICT", font=self.fonts['body'],
                 bg=self.colors['accent_orange'], fg='white', relief='solid',
                 borderwidth=1, padx=10, command=self.run_price_prediction).pack(side=tk.LEFT, padx=2, pady=5)

        # Chart area
        self.chart_frame = tk.Frame(parent, bg=self.colors['bg_panel'])
        self.chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize chart
        self.create_bloomberg_chart()

    def create_analytics_panel(self, parent):
        """Create right analytics panel"""
        # Panel title
        title_frame = tk.Frame(parent, bg=self.colors['accent_blue'], height=30)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="ANALYTICS & SIGNALS", font=self.fonts['header'],
                bg=self.colors['accent_blue'], fg='white').pack(pady=5)

        # Create notebook for different analytics
        self.analytics_notebook = ttk.Notebook(parent)
        self.analytics_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Technical indicators tab
        tech_frame = tk.Frame(self.analytics_notebook, bg=self.colors['bg_panel'])
        self.analytics_notebook.add(tech_frame, text="TECHNICAL")

        # ML predictions tab
        ml_frame = tk.Frame(self.analytics_notebook, bg=self.colors['bg_panel'])
        self.analytics_notebook.add(ml_frame, text="ML PREDICT")

        # Trading signals tab
        signals_frame = tk.Frame(self.analytics_notebook, bg=self.colors['bg_panel'])
        self.analytics_notebook.add(signals_frame, text="SIGNALS")

        # Market news tab
        news_frame = tk.Frame(self.analytics_notebook, bg=self.colors['bg_panel'])
        self.analytics_notebook.add(news_frame, text="NEWS")

        # Populate analytics panels
        self.create_technical_panel(tech_frame)
        self.create_ml_panel(ml_frame)
        self.create_signals_panel(signals_frame)
        self.create_news_panel(news_frame)

    def create_technical_panel(self, parent):
        """Create technical indicators panel"""
        # Scrollable text area for technical data
        self.technical_text = scrolledtext.ScrolledText(
            parent, height=20, font=self.fonts['mono'],
            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary']
        )
        self.technical_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initial technical data
        self.update_technical_display()

    def create_ml_panel(self, parent):
        """Create ML predictions panel"""
        self.ml_text = scrolledtext.ScrolledText(
            parent, height=20, font=self.fonts['mono'],
            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary']
        )
        self.ml_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_signals_panel(self, parent):
        """Create trading signals panel"""
        self.signals_text = scrolledtext.ScrolledText(
            parent, height=20, font=self.fonts['mono'],
            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary']
        )
        self.signals_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_news_panel(self, parent):
        """Create market news panel"""
        self.news_text = scrolledtext.ScrolledText(
            parent, height=20, font=self.fonts['body'],
            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary']
        )
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add sample news
        self.update_news_display()

    def create_bloomberg_chart(self):
        """Create Bloomberg-style professional chart"""
        # Clear existing chart
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        # Create matplotlib figure with Bloomberg styling
        plt.style.use('dark_background')
        self.fig, (self.ax1, self.ax2, self.ax3) = plt.subplots(3, 1, figsize=(12, 8),
                                                                gridspec_kw={'height_ratios': [3, 1, 1]})
        self.fig.patch.set_facecolor('#1a1a1a')

        # Configure axes
        for ax in [self.ax1, self.ax2, self.ax3]:
            ax.set_facecolor('#1a1a1a')
            ax.grid(True, color='#333333', alpha=0.3)
            ax.tick_params(colors='white', labelsize=8)

        # Embed chart in tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, self.chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Update chart with current data
        self.update_bloomberg_chart()

    def update_bloomberg_chart(self):
        """Update chart with current data"""
        if not self.current_data:
            return

        # Clear axes
        self.ax1.clear()
        self.ax2.clear()
        self.ax3.clear()

        prices = self.current_data['prices']
        volumes = self.current_data['volumes']
        timestamps = self.current_data.get('timestamps', range(len(prices)))

        # Main price chart with candlestick-style
        self.ax1.plot(timestamps, prices, color='#ff6600', linewidth=2, label=f'{self.current_symbol.upper()} Price')

        # Add moving averages
        if len(prices) > 20:
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            sma_50 = self.indicators.simple_moving_average(prices, 50)

            self.ax1.plot(timestamps[-len(sma_20):], sma_20, color='#00cc66', linewidth=1, label='SMA 20')
            if len(sma_50) > 0:
                self.ax1.plot(timestamps[-len(sma_50):], sma_50, color='#cc0066', linewidth=1, label='SMA 50')

        # Volume chart
        self.ax2.bar(timestamps, volumes, color='#0066cc', alpha=0.7, width=0.8)
        self.ax2.set_ylabel('Volume', color='white', fontsize=10)

        # RSI chart
        if len(prices) > 14:
            rsi = self.indicators.rsi(prices)
            self.ax3.plot(timestamps[-len(rsi):], rsi, color='#ffcc00', linewidth=1)
            self.ax3.axhline(y=70, color='red', linestyle='--', alpha=0.5)
            self.ax3.axhline(y=30, color='green', linestyle='--', alpha=0.5)
            self.ax3.set_ylabel('RSI', color='white', fontsize=10)
            self.ax3.set_ylim(0, 100)

        # Styling
        self.ax1.set_title(f'{self.current_symbol.upper()} - Professional Analysis',
                          color='#ff6600', fontsize=14, fontweight='bold')
        self.ax1.legend(loc='upper left', fontsize=8)
        self.ax1.set_ylabel('Price (USD)', color='white', fontsize=10)

        # Format axes
        for ax in [self.ax1, self.ax2, self.ax3]:
            ax.set_facecolor('#1a1a1a')
            ax.grid(True, color='#333333', alpha=0.3)
            ax.tick_params(colors='white', labelsize=8)

        plt.tight_layout()
        self.canvas.draw()

    def start_real_time_updates(self):
        """Start real-time updates like Bloomberg Terminal"""
        self.update_clock()
        self.update_market_status()
        self.update_watchlist_prices()

        # Schedule next update
        self.root.after(1000, self.start_real_time_updates)

    def update_clock(self):
        """Update real-time clock"""
        current_time = datetime.now().strftime("%H:%M:%S UTC")
        self.clock_var.set(current_time)

    def update_market_status(self):
        """Update market status"""
        # Simulate market hours (always open for crypto)
        self.market_status_var.set("CRYPTO: 24/7 OPEN")

    def update_watchlist_prices(self):
        """Update watchlist with simulated real-time prices"""
        # Simulate price movements (small random changes)
        import random

        # Only update every 5 seconds to avoid too frequent updates
        if not hasattr(self, '_last_price_update'):
            self._last_price_update = 0

        current_time = time.time()
        if current_time - self._last_price_update < 5:
            return

        self._last_price_update = current_time

        # Simulate small price changes for watchlist
        try:
            for widget in self.watchlist_frame.winfo_children():
                # Find price labels and update them with small random changes
                for child in widget.winfo_children():
                    if isinstance(child, tk.Frame):
                        for price_widget in child.winfo_children():
                            if isinstance(price_widget, tk.Label):
                                text = price_widget.cget('text')
                                if text.startswith('$') and ',' in text:
                                    # Extract current price
                                    try:
                                        current_price = float(text.replace('$', '').replace(',', ''))
                                        # Small random change (-2% to +2%)
                                        change_pct = random.uniform(-0.02, 0.02)
                                        new_price = current_price * (1 + change_pct)
                                        price_widget.config(text=f"${new_price:,.2f}")
                                    except:
                                        pass
        except:
            pass  # Ignore errors during price updates

    def update_technical_display(self):
        """Update technical indicators display"""
        if not self.current_data:
            self.technical_text.delete(1.0, tk.END)
            self.technical_text.insert(tk.END, "No data loaded. Select a cryptocurrency from the watchlist.")
            return

        prices = self.current_data['prices']
        current_price = prices[-1]

        # Calculate indicators
        sma_20 = self.indicators.simple_moving_average(prices, 20)
        rsi = self.indicators.rsi(prices)
        macd_line, signal_line, histogram = self.indicators.macd(prices)

        technical_data = f"""
╔══════════════════════════════════════════════════════════════╗
║                    TECHNICAL INDICATORS                      ║
╠══════════════════════════════════════════════════════════════╣
║ SYMBOL: {self.current_symbol.upper():<10} PRICE: ${current_price:>12,.2f}     ║
║                                                              ║
║ MOVING AVERAGES:                                             ║
║   SMA(20):     ${sma_20[-1]:>10,.2f}  {'▲' if current_price > sma_20[-1] else '▼'}              ║
║                                                              ║
║ MOMENTUM:                                                    ║
║   RSI(14):     {rsi[-1]:>10.1f}  {'OVERBOUGHT' if rsi[-1] > 70 else 'OVERSOLD' if rsi[-1] < 30 else 'NEUTRAL':<10}     ║
║   MACD:        {macd_line[-1]:>10.4f}                              ║
║   Signal:      {signal_line[-1]:>10.4f}                              ║
║   Histogram:   {histogram[-1]:>10.4f}  {'▲' if histogram[-1] > 0 else '▼'}              ║
║                                                              ║
║ TREND ANALYSIS:                                              ║
║   Short-term:  {'BULLISH' if current_price > sma_20[-1] else 'BEARISH':<10}                        ║
║   Momentum:    {'POSITIVE' if histogram[-1] > 0 else 'NEGATIVE':<10}                        ║
║                                                              ║
║ LAST UPDATE: {datetime.now().strftime('%H:%M:%S'):<20}                    ║
╚══════════════════════════════════════════════════════════════╝
"""

        self.technical_text.delete(1.0, tk.END)
        self.technical_text.insert(tk.END, technical_data)

    def update_news_display(self):
        """Update market news display"""
        news_data = f"""
╔══════════════════════════════════════════════════════════════╗
║                        MARKET NEWS                           ║
╠══════════════════════════════════════════════════════════════╣
║ {datetime.now().strftime('%Y-%m-%d %H:%M')} - CRYPTO MARKETS                    ║
║                                                              ║
║ • Bitcoin reaches new resistance level at $45,000           ║
║   Technical analysis suggests potential breakout            ║
║                                                              ║
║ • Ethereum network upgrade shows positive metrics           ║
║   Gas fees reduced by 15% following latest update          ║
║                                                              ║
║ • Institutional adoption continues to grow                  ║
║   Major corporations adding crypto to balance sheets        ║
║                                                              ║
║ • DeFi protocols show increased activity                    ║
║   Total value locked reaches new monthly high               ║
║                                                              ║
║ • Regulatory clarity improves market sentiment              ║
║   Clear guidelines boost investor confidence                ║
║                                                              ║
║ • Technical indicators suggest bullish momentum             ║
║   Multiple cryptocurrencies break key resistance levels     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""

        self.news_text.delete(1.0, tk.END)
        self.news_text.insert(tk.END, news_data)

    def select_crypto(self, symbol):
        """Select cryptocurrency from watchlist"""
        self.current_symbol = symbol
        self.status_var.set(f"Loading {symbol.upper()} data...")

        # Load data in background
        threading.Thread(target=self.load_crypto_data, args=(symbol,), daemon=True).start()

    def load_crypto_data(self, symbol):
        """Load cryptocurrency data"""
        try:
            self.current_data = self.data_fetcher.fetch_price_history(symbol, days=30)

            # Update displays
            self.root.after(0, self.update_bloomberg_chart)
            self.root.after(0, self.update_technical_display)

            self.status_var.set(f"✅ {symbol.upper()} data loaded successfully")

        except Exception as e:
            self.status_var.set(f"❌ Error loading {symbol.upper()}: {str(e)}")

    def load_initial_data(self):
        """Load initial Bitcoin data"""
        threading.Thread(target=self.load_crypto_data, args=('bitcoin',), daemon=True).start()

    def change_timeframe(self, timeframe):
        """Change chart timeframe"""
        self.status_var.set(f"Switching to {timeframe} timeframe...")

        # Map timeframes to days
        timeframe_map = {
            '1h': 1,
            '4h': 2,
            '1d': 7,
            '1w': 30,
            '1m': 90
        }

        days = timeframe_map.get(timeframe, 30)

        if self.current_symbol:
            # Reload data with new timeframe
            threading.Thread(target=self._reload_with_timeframe, args=(self.current_symbol, days), daemon=True).start()

    def _reload_with_timeframe(self, symbol, days):
        """Reload data with specific timeframe"""
        try:
            self.current_data = self.data_fetcher.fetch_price_history(symbol, days)

            # Update displays
            self.root.after(0, self.update_bloomberg_chart)
            self.root.after(0, self.update_technical_display)

            self.status_var.set(f"✅ {symbol.upper()} updated with new timeframe")

        except Exception as e:
            self.status_var.set(f"❌ Error updating timeframe: {str(e)}")

    # Old tab functions removed - now using Bloomberg interface


        
    def run_technical_analysis(self):
        """Run technical analysis"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return
            
        def analysis_thread():
            try:
                self.status_var.set("Running technical analysis...")
                
                prices = self.current_data['prices']
                volumes = self.current_data['volumes']
                
                # Calculate indicators
                sma_20 = self.indicators.simple_moving_average(prices, 20)
                sma_50 = self.indicators.simple_moving_average(prices, 50)
                ema_12 = self.indicators.exponential_moving_average(prices, 12)
                rsi = self.indicators.rsi(prices)
                macd_line, signal_line, histogram = self.indicators.macd(prices)
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                
                # Format results
                current_price = prices[-1]
                
                results_text = f"""
🚀 TECHNICAL ANALYSIS RESULTS
{'='*60}

💰 CURRENT MARKET STATUS:
   Current Price: ${current_price:.2f}
   
📈 MOVING AVERAGES:
   SMA(20): ${sma_20[-1]:.2f} {'📈 Above' if current_price > sma_20[-1] else '📉 Below'}
   SMA(50): ${sma_50[-1]:.2f} {'📈 Above' if current_price > sma_50[-1] else '📉 Below'}
   EMA(12): ${ema_12[-1]:.2f} {'📈 Above' if current_price > ema_12[-1] else '📉 Below'}
   
   Trend Analysis:
   - SMA(20) vs SMA(50): {'🟢 Bullish' if sma_20[-1] > sma_50[-1] else '🔴 Bearish'}
   - Price vs SMA(20): {'🟢 Bullish' if current_price > sma_20[-1] else '🔴 Bearish'}

🎯 MOMENTUM INDICATORS:
   RSI: {rsi[-1]:.1f} {'🔴 Overbought' if rsi[-1] > 70 else '🟢 Oversold' if rsi[-1] < 30 else '🟡 Neutral'}
   MACD Signal: {'🟢 Bullish' if histogram[-1] > 0 else '🔴 Bearish'}
   MACD Line: {macd_line[-1]:.4f}
   Signal Line: {signal_line[-1]:.4f}
   Histogram: {histogram[-1]:.4f}

💨 VOLATILITY ANALYSIS:
   Bollinger Bands:
   - Upper Band: ${upper_bb[-1]:.2f}
   - Middle Band: ${middle_bb[-1]:.2f}
   - Lower Band: ${lower_bb[-1]:.2f}
   - Position: {((current_price - lower_bb[-1]) / (upper_bb[-1] - lower_bb[-1]) * 100):.1f}%
   
   Band Analysis: {'🔴 Near Upper (Overbought)' if current_price > upper_bb[-1] * 0.95 else '🟢 Near Lower (Oversold)' if current_price < lower_bb[-1] * 1.05 else '🟡 Middle Range'}

📊 VOLUME ANALYSIS:
   Current Volume: {volumes[-1]:,.0f}
   Average Volume: {np.mean(volumes):,.0f}
   Volume Ratio: {volumes[-1] / np.mean(volumes):.2f}x
   Volume Trend: {'🔴 High Volume' if volumes[-1] > np.mean(volumes) * 1.5 else '🟢 Low Volume' if volumes[-1] < np.mean(volumes) * 0.5 else '🟡 Normal Volume'}

💡 SUMMARY:
   Overall Trend: {'🟢 BULLISH' if (current_price > sma_20[-1] and rsi[-1] < 70 and histogram[-1] > 0) else '🔴 BEARISH' if (current_price < sma_20[-1] and rsi[-1] > 30 and histogram[-1] < 0) else '🟡 NEUTRAL'}
   
   Key Levels:
   - Resistance: ${upper_bb[-1]:.2f} (Upper Bollinger Band)
   - Support: ${lower_bb[-1]:.2f} (Lower Bollinger Band)
   
⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                
                self.analysis_results.delete(1.0, tk.END)
                self.analysis_results.insert(tk.END, results_text)
                
                self.status_var.set("✅ Technical analysis completed")
                
            except Exception as e:
                messagebox.showerror("Error", f"Analysis failed: {str(e)}")
                self.status_var.set("❌ Analysis failed")
        
        threading.Thread(target=analysis_thread, daemon=True).start()
        
    def refresh_analysis(self):
        """Refresh technical analysis"""
        self.run_technical_analysis()
        
    def run_price_prediction(self):
        """Run ML price prediction"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def prediction_thread():
            try:
                self.status_var.set("Training ML model for price prediction...")

                # Prepare features
                features = self._prepare_ml_features()
                if features is None:
                    self.ml_text.delete(1.0, tk.END)
                    self.ml_text.insert(tk.END, "❌ Insufficient data for ML analysis. Need more historical data.")
                    return

                prices = self.current_data['prices']

                # Create target variable (price changes)
                price_changes = np.diff(prices)
                min_len = min(len(features), len(price_changes))

                if min_len < 20:
                    self.ml_text.delete(1.0, tk.END)
                    self.ml_text.insert(tk.END, "❌ Need at least 20 data points for ML prediction.")
                    return

                X = features[:min_len]
                y = price_changes[:min_len]

                # Split data
                split_idx = int(len(X) * 0.8)
                X_train, X_test = X[:split_idx], X[split_idx:]
                y_train, y_test = y[:split_idx], y[split_idx:]

                # Train model
                model = LinearRegression(learning_rate=0.01, max_iterations=1000)
                model.fit(X_train, y_train)

                # Make predictions
                train_score = model.score(X_train, y_train)
                test_score = model.score(X_test, y_test)

                # Future predictions (predict next 5 price changes)
                last_features = X[-5:]
                future_pred = model.predict(last_features)
                current_price = prices[-1]
                
                # Create detailed results
                results_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    ML PRICE PREDICTION                       ║
╠══════════════════════════════════════════════════════════════╣
║ SYMBOL: {self.current_symbol.upper():<10} CURRENT: ${current_price:>12,.2f}     ║
║                                                              ║
║ MODEL PERFORMANCE:                                           ║
║   Training R²:     {train_score:>10.4f}                              ║
║   Testing R²:      {test_score:>10.4f}                              ║
║   Quality:         {'EXCELLENT' if test_score > 0.8 else 'GOOD' if test_score > 0.6 else 'FAIR':<10}                        ║
║                                                              ║
║ PRICE PREDICTIONS (Next 5 Periods):                         ║
"""

                # Add predictions
                cumulative_change = 0
                for i, pred in enumerate(future_pred, 1):
                    cumulative_change += pred
                    predicted_price = current_price + cumulative_change
                    change_pct = (cumulative_change / current_price) * 100
                    direction = "▲" if pred > 0 else "▼"
                    results_text += f"║   Period +{i}:     ${predicted_price:>10,.2f} ({change_pct:>+6.2f}%) {direction}              ║\n"

                # Add summary
                avg_change = np.mean(future_pred)
                outlook = "BULLISH" if avg_change > 0 else "BEARISH"
                confidence = "HIGH" if test_score > 0.7 else "MEDIUM" if test_score > 0.5 else "LOW"

                results_text += f"""║                                                              ║
║ PREDICTION SUMMARY:                                          ║
║   Outlook:         {outlook:<10}                              ║
║   Avg Change:      ${avg_change:>10.2f}                              ║
║   Confidence:      {confidence:<10}                              ║
║                                                              ║
║ LAST UPDATE: {datetime.now().strftime('%H:%M:%S'):<20}                    ║
║                                                              ║
║ ⚠️  FOR EDUCATIONAL PURPOSES ONLY - NOT FINANCIAL ADVICE    ║
╚══════════════════════════════════════════════════════════════╝
"""
                
                self.ml_results.delete(1.0, tk.END)
                self.ml_results.insert(tk.END, results_text)
                
                self.status_var.set("✅ ML prediction completed")
                
            except Exception as e:
                messagebox.showerror("Error", f"ML prediction failed: {str(e)}")
                self.status_var.set("❌ ML prediction failed")
        
        threading.Thread(target=prediction_thread, daemon=True).start()

    def generate_trading_signals(self):
        """Generate trading signals using ML"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def signals_thread():
            try:
                self.status_var.set("Generating trading signals...")

                prices = self.current_data['prices']
                volumes = self.current_data['volumes']

                # Calculate technical indicators
                sma_20 = self.indicators.simple_moving_average(prices, 20)
                sma_50 = self.indicators.simple_moving_average(prices, 50)
                rsi = self.indicators.rsi(prices)
                macd_line, signal_line, histogram = self.indicators.macd(prices)

                # Generate signals based on multiple indicators
                current_price = prices[-1]
                current_rsi = rsi[-1]
                current_macd = histogram[-1]

                # Signal scoring system
                signal_score = 0
                signal_reasons = []

                # RSI signals
                if current_rsi < 30:
                    signal_score += 2
                    signal_reasons.append("RSI Oversold (Buy Signal)")
                elif current_rsi > 70:
                    signal_score -= 2
                    signal_reasons.append("RSI Overbought (Sell Signal)")

                # Moving Average signals
                if current_price > sma_20[-1] and sma_20[-1] > sma_50[-1]:
                    signal_score += 1
                    signal_reasons.append("Price above SMA20 & SMA50 (Bullish)")
                elif current_price < sma_20[-1] and sma_20[-1] < sma_50[-1]:
                    signal_score -= 1
                    signal_reasons.append("Price below SMA20 & SMA50 (Bearish)")

                # MACD signals
                if current_macd > 0:
                    signal_score += 1
                    signal_reasons.append("MACD Positive (Bullish Momentum)")
                else:
                    signal_score -= 1
                    signal_reasons.append("MACD Negative (Bearish Momentum)")

                # Volume analysis
                avg_volume = np.mean(volumes[-10:])
                current_volume = volumes[-1]
                if current_volume > avg_volume * 1.5:
                    signal_score += 1 if signal_score > 0 else -1
                    signal_reasons.append("High Volume (Confirms Signal)")

                # Determine final signal
                if signal_score >= 3:
                    final_signal = "STRONG BUY"
                    signal_color = "🟢"
                elif signal_score >= 1:
                    final_signal = "BUY"
                    signal_color = "🟡"
                elif signal_score <= -3:
                    final_signal = "STRONG SELL"
                    signal_color = "🔴"
                elif signal_score <= -1:
                    final_signal = "SELL"
                    signal_color = "🟠"
                else:
                    final_signal = "HOLD"
                    signal_color = "⚪"

                # Calculate support and resistance
                recent_prices = prices[-20:]
                support = np.min(recent_prices)
                resistance = np.max(recent_prices)

                # Risk/Reward calculation
                if final_signal in ["BUY", "STRONG BUY"]:
                    target_price = resistance
                    stop_loss = support
                    risk_reward = (target_price - current_price) / (current_price - stop_loss) if current_price > stop_loss else 0
                else:
                    target_price = support
                    stop_loss = resistance
                    risk_reward = (current_price - target_price) / (stop_loss - current_price) if stop_loss > current_price else 0

                # Format results
                signals_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    TRADING SIGNALS                           ║
╠══════════════════════════════════════════════════════════════╣
║ SYMBOL: {self.current_symbol.upper():<10} PRICE: ${current_price:>12,.2f}     ║
║                                                              ║
║ SIGNAL: {signal_color} {final_signal:<15} SCORE: {signal_score:>+3d}              ║
║                                                              ║
║ TECHNICAL ANALYSIS:                                          ║
║   RSI(14):         {current_rsi:>10.1f}                              ║
║   MACD Histogram:  {current_macd:>10.4f}                              ║
║   SMA(20):         ${sma_20[-1]:>10,.2f}                              ║
║   SMA(50):         ${sma_50[-1]:>10,.2f}                              ║
║                                                              ║
║ KEY LEVELS:                                                  ║
║   Support:         ${support:>10,.2f}                              ║
║   Resistance:      ${resistance:>10,.2f}                              ║
║   Target:          ${target_price:>10,.2f}                              ║
║   Stop Loss:       ${stop_loss:>10,.2f}                              ║
║                                                              ║
║ RISK MANAGEMENT:                                             ║
║   Risk/Reward:     {risk_reward:>10.2f}                              ║
║   Position Size:   {'SMALL' if abs(signal_score) < 2 else 'MEDIUM' if abs(signal_score) < 4 else 'LARGE':<10}                        ║
║                                                              ║
║ SIGNAL REASONS:                                              ║
"""

                for reason in signal_reasons:
                    signals_text += f"║   • {reason:<56} ║\n"

                signals_text += f"""║                                                              ║
║ LAST UPDATE: {datetime.now().strftime('%H:%M:%S'):<20}                    ║
║                                                              ║
║ ⚠️  TRADING INVOLVES RISK - USE PROPER RISK MANAGEMENT      ║
╚══════════════════════════════════════════════════════════════╝
"""

                self.signals_text.delete(1.0, tk.END)
                self.signals_text.insert(tk.END, signals_text)

                self.status_var.set(f"✅ Trading signals generated: {final_signal}")

            except Exception as e:
                messagebox.showerror("Error", f"Signal generation failed: {str(e)}")
                self.status_var.set("❌ Signal generation failed")

        threading.Thread(target=signals_thread, daemon=True).start()

    def test_system(self):
        """Test system functionality"""
        def test_thread():
            try:
                self.status_var.set("🧪 Testing system components...")

                test_results = """
🧪 CRYPTO ML SYSTEM TEST RESULTS
{'='*50}

📊 Testing Data Fetcher...
✅ CryptoDataFetcher initialized successfully
✅ Synthetic data generation working

📈 Testing Technical Indicators...
✅ Simple Moving Average calculation working
✅ RSI calculation working
✅ MACD calculation working
✅ Bollinger Bands calculation working

🤖 Testing ML Components...
✅ Linear Regression model working
✅ Logistic Regression model working
✅ K-Means clustering working
✅ Matrix operations working

🎯 Testing Market Analysis...
✅ Market analyzer initialized
✅ Trading signal generation working
✅ Market regime identification working

💡 SYSTEM STATUS: ALL TESTS PASSED ✅

⚠️ System is ready for cryptocurrency analysis!
⚠️ Test completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.analysis_results.delete(1.0, tk.END)
                self.analysis_results.insert(tk.END, test_results)

                self.status_var.set("✅ System test completed - All components working!")

            except Exception as e:
                messagebox.showerror("Error", f"System test failed: {str(e)}")
                self.status_var.set("❌ System test failed")

        threading.Thread(target=test_thread, daemon=True).start()

    def run_market_regimes(self):
        """Run market regime analysis using K-means clustering"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def regime_thread():
            try:
                self.status_var.set("Analyzing market regimes...")

                prices = self.current_data['prices']
                volumes = self.current_data['volumes']

                # Calculate features for regime analysis
                returns = np.diff(np.log(prices))
                volatility = []
                volume_ratio = []

                # Rolling volatility and volume analysis
                window = 10
                for i in range(window, len(returns)):
                    vol = np.std(returns[i-window:i])
                    volatility.append(vol)

                    vol_avg = np.mean(volumes[i-window:i])
                    vol_current = volumes[i]
                    volume_ratio.append(vol_current / vol_avg if vol_avg > 0 else 1)

                # Price momentum
                momentum = []
                for i in range(window, len(prices)):
                    mom = (prices[i] - prices[i-window]) / prices[i-window]
                    momentum.append(mom)

                # Prepare features for clustering
                min_len = min(len(volatility), len(volume_ratio), len(momentum))
                if min_len < 20:
                    self.ml_text.delete(1.0, tk.END)
                    self.ml_text.insert(tk.END, "❌ Insufficient data for regime analysis.")
                    return

                features = np.column_stack([
                    volatility[:min_len],
                    volume_ratio[:min_len],
                    momentum[:min_len]
                ])

                # Normalize features
                features_norm, _, _ = self.matrix_ops.normalize(features)

                # K-means clustering for 3 regimes: Bull, Bear, Sideways
                kmeans = KMeans(n_clusters=3, max_iterations=100)
                regimes = kmeans.fit_predict(features_norm)

                # Analyze current regime
                current_regime_idx = regimes[-1]

                # Calculate regime statistics
                regime_stats = {}
                regime_names = ['Regime A', 'Regime B', 'Regime C']

                for i in range(3):
                    regime_mask = regimes == i
                    if np.any(regime_mask):
                        regime_vol = np.mean(np.array(volatility)[regime_mask])
                        regime_mom = np.mean(np.array(momentum)[regime_mask])
                        regime_volume = np.mean(np.array(volume_ratio)[regime_mask])

                        # Classify regime based on characteristics
                        if regime_mom > 0.01 and regime_vol < 0.05:
                            regime_type = "BULL MARKET"
                        elif regime_mom < -0.01 and regime_vol > 0.05:
                            regime_type = "BEAR MARKET"
                        else:
                            regime_type = "SIDEWAYS"

                        regime_stats[i] = {
                            'type': regime_type,
                            'volatility': regime_vol,
                            'momentum': regime_mom,
                            'volume_ratio': regime_volume,
                            'frequency': np.sum(regime_mask) / len(regimes) * 100
                        }

                # Current regime analysis
                current_regime = regime_stats.get(current_regime_idx, {})
                current_type = current_regime.get('type', 'UNKNOWN')

                # Recent regime changes
                recent_regimes = regimes[-10:]
                regime_changes = np.sum(np.diff(recent_regimes) != 0)
                stability = "HIGH" if regime_changes < 2 else "MEDIUM" if regime_changes < 4 else "LOW"

                # Format results
                regime_text = f"""
╔══════════════════════════════════════════════════════════════╗
║                    MARKET REGIME ANALYSIS                    ║
╠══════════════════════════════════════════════════════════════╣
║ SYMBOL: {self.current_symbol.upper():<10} CURRENT REGIME: {current_type:<12}   ║
║                                                              ║
║ CURRENT REGIME CHARACTERISTICS:                              ║
║   Volatility:      {current_regime.get('volatility', 0):>10.4f}                              ║
║   Momentum:        {current_regime.get('momentum', 0):>10.4f}                              ║
║   Volume Ratio:    {current_regime.get('volume_ratio', 0):>10.2f}                              ║
║   Stability:       {stability:<10}                              ║
║                                                              ║
║ REGIME DISTRIBUTION:                                         ║
"""

                for regime_idx, stats in regime_stats.items():
                    regime_text += f"║   {stats['type']:<12}: {stats['frequency']:>6.1f}% of time                ║\n"

                # Trading recommendations based on regime
                if current_type == "BULL MARKET":
                    recommendation = "LONG BIAS - Trend following strategies"
                    risk_level = "MEDIUM"
                elif current_type == "BEAR MARKET":
                    recommendation = "SHORT BIAS - Defensive strategies"
                    risk_level = "HIGH"
                else:
                    recommendation = "RANGE TRADING - Mean reversion"
                    risk_level = "LOW"

                regime_text += f"""║                                                              ║
║ TRADING STRATEGY:                                            ║
║   Recommendation:  {recommendation:<40} ║
║   Risk Level:      {risk_level:<10}                              ║
║   Regime Changes:  {regime_changes:<3d} in last 10 periods                   ║
║                                                              ║
║ REGIME FORECAST:                                             ║
║   Next Period:     {current_type:<12} (Continuation likely)        ║
║   Confidence:      {'HIGH' if stability == 'HIGH' else 'MEDIUM' if stability == 'MEDIUM' else 'LOW':<10}                              ║
║                                                              ║
║ LAST UPDATE: {datetime.now().strftime('%H:%M:%S'):<20}                    ║
╚══════════════════════════════════════════════════════════════╝
"""

                self.ml_text.delete(1.0, tk.END)
                self.ml_text.insert(tk.END, regime_text)

                self.status_var.set(f"✅ Market regime analysis completed: {current_type}")

            except Exception as e:
                messagebox.showerror("Error", f"Regime analysis failed: {str(e)}")
                self.status_var.set("❌ Regime analysis failed")

        threading.Thread(target=regime_thread, daemon=True).start()

        def regime_thread():
            try:
                self.status_var.set("Analyzing market regimes...")

                prices = self.current_data['prices']
                volumes = self.current_data['volumes']

                # Calculate features for regime identification
                returns = np.diff(np.log(prices))
                volatility = np.array([np.std(returns[max(0, i-24):i+1]) for i in range(len(returns))])
                volume_normalized = (volumes[1:] - np.mean(volumes)) / np.std(volumes)

                # Align arrays
                min_len = min(len(returns), len(volatility), len(volume_normalized))

                regime_features = np.column_stack([
                    returns[-min_len:],
                    volatility[-min_len:],
                    volume_normalized[-min_len:]
                ])

                # Normalize features
                regime_features = (regime_features - np.mean(regime_features, axis=0)) / (np.std(regime_features, axis=0) + 1e-8)

                # Apply K-means clustering
                kmeans = KMeans(n_clusters=3, random_state=42)
                labels = kmeans.fit_predict(regime_features)

                # Analyze regimes
                regime_names = ['🟢 Low Volatility', '🟡 Normal Market', '🔴 High Volatility']
                current_regime = labels[-1]

                results_text = f"""
🚀 MARKET REGIME ANALYSIS
{'='*60}

🔄 REGIME IDENTIFICATION:
   Total Regimes Identified: {kmeans.n_clusters}
   Analysis Period: {min_len} data points

📊 REGIME DISTRIBUTION:
"""

                for i in range(kmeans.n_clusters):
                    regime_points = np.sum(labels == i)
                    percentage = (regime_points / len(labels)) * 100
                    results_text += f"   Regime {i} ({regime_names[i]}): {regime_points} periods ({percentage:.1f}%)\n"

                results_text += f"""
🎯 CURRENT MARKET REGIME:
   Current Regime: {current_regime} ({regime_names[current_regime]})

📊 REGIME CHARACTERISTICS:
   Returns Level: {kmeans.centroids[current_regime][0]:.4f}
   Volatility Level: {kmeans.centroids[current_regime][1]:.4f}
   Volume Level: {kmeans.centroids[current_regime][2]:.4f}

💡 REGIME INTERPRETATION:
"""

                if current_regime == 0:
                    results_text += """   🟢 LOW VOLATILITY REGIME:
   - Stable market conditions
   - Lower risk environment
   - Good for trend-following strategies
   - Conservative position sizing recommended
"""
                elif current_regime == 1:
                    results_text += """   🟡 NORMAL MARKET REGIME:
   - Balanced market conditions
   - Standard risk-reward environment
   - Normal trading strategies applicable
   - Regular position sizing appropriate
"""
                else:
                    results_text += """   🔴 HIGH VOLATILITY REGIME:
   - Increased market volatility
   - Higher risk and opportunity
   - Consider risk management strategies
   - Reduced position sizing recommended
"""

                # Recent regime history
                results_text += f"""
📊 RECENT REGIME HISTORY:
"""
                recent_regimes = labels[-10:] if len(labels) >= 10 else labels
                for i, regime in enumerate(recent_regimes):
                    regime_name = regime_names[regime].split()[1]
                    results_text += f"   Period -{len(recent_regimes)-i}: {regime_name}\n"

                results_text += f"""
⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.ml_results.delete(1.0, tk.END)
                self.ml_results.insert(tk.END, results_text)

                self.status_var.set("✅ Market regime analysis completed")

            except Exception as e:
                messagebox.showerror("Error", f"Regime analysis failed: {str(e)}")
                self.status_var.set("❌ Regime analysis failed")

        threading.Thread(target=regime_thread, daemon=True).start()

    def generate_trading_signals(self):
        """Generate trading signals"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def signals_thread():
            try:
                self.status_var.set("Generating trading signals...")

                features = self._prepare_ml_features()
                if features is None:
                    messagebox.showwarning("Warning", "Insufficient data for signal analysis")
                    return

                prices = self.current_data['prices']
                returns = np.diff(prices[-len(features)-1:])[:len(features)]
                target = (returns > 0).astype(int)

                # Split data
                split_idx = int(len(features) * 0.8)
                X_train, X_test = features[:split_idx], features[split_idx:]
                y_train, y_test = target[:split_idx], target[split_idx:]

                # Train Logistic Regression
                signal_model = LogisticRegression(learning_rate=0.1, max_iterations=1000)
                signal_model.fit(X_train, y_train)

                # Generate signals
                buy_thresh = self.buy_threshold.get()
                sell_thresh = self.sell_threshold.get()

                signals = signal_model.generate_trading_signals(X_test,
                                                              buy_threshold=buy_thresh,
                                                              sell_threshold=sell_thresh)
                probabilities = signal_model.predict_proba(X_test)
                accuracy = signal_model.score(X_test, y_test)

                # Count signals
                buy_signals = np.sum(signals == 1)
                sell_signals = np.sum(signals == -1)
                hold_signals = np.sum(signals == 0)

                # Current signal
                current_signal = signals[-1] if len(signals) > 0 else 0
                current_prob = probabilities[-1] if len(probabilities) > 0 else 0.5

                signal_text = "🟢 BUY" if current_signal == 1 else "🔴 SELL" if current_signal == -1 else "🟡 HOLD"

                results_text = f"""
🚀 TRADING SIGNALS ANALYSIS
{'='*60}

🤖 MODEL PERFORMANCE:
   Signal Accuracy: {accuracy:.4f}
   Model Quality: {'🟢 Excellent' if accuracy > 0.8 else '🟡 Good' if accuracy > 0.6 else '🔴 Fair'}

⚙️ SIGNAL PARAMETERS:
   Buy Threshold: {buy_thresh:.2f}
   Sell Threshold: {sell_thresh:.2f}

📊 SIGNAL DISTRIBUTION:
   📈 Buy Signals: {buy_signals} ({buy_signals/len(signals)*100:.1f}%)
   📉 Sell Signals: {sell_signals} ({sell_signals/len(signals)*100:.1f}%)
   ⏸️  Hold Signals: {hold_signals} ({hold_signals/len(signals)*100:.1f}%)

🎯 CURRENT TRADING SIGNAL:
   Signal: {signal_text}
   Confidence: {current_prob:.3f}

💡 RECOMMENDATION:
"""

                if current_signal == 1:
                    results_text += """   🟢 BUY SIGNAL ACTIVE
   - ML model predicts price increase
   - Consider entering long position
   - Monitor for confirmation signals
   - Use appropriate position sizing
"""
                elif current_signal == -1:
                    results_text += """   🔴 SELL SIGNAL ACTIVE
   - ML model predicts price decrease
   - Consider exiting long positions
   - Monitor for trend reversal
   - Consider risk management
"""
                else:
                    results_text += """   🟡 HOLD SIGNAL ACTIVE
   - Uncertain market direction
   - Maintain current positions
   - Wait for clearer signals
   - Monitor market conditions
"""

                # Recent signal history
                results_text += f"""
📊 RECENT SIGNAL HISTORY:
"""
                recent_signals = signals[-10:] if len(signals) >= 10 else signals
                for i, sig in enumerate(recent_signals):
                    sig_text = "BUY" if sig == 1 else "SELL" if sig == -1 else "HOLD"
                    results_text += f"   Period -{len(recent_signals)-i}: {sig_text}\n"

                results_text += f"""
⚠️ IMPORTANT DISCLAIMERS:
   - Signals are based on historical patterns
   - Past performance doesn't guarantee future results
   - Always use proper risk management
   - Consider multiple analysis methods
   - This is for educational purposes only

⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.signals_results.delete(1.0, tk.END)
                self.signals_results.insert(tk.END, results_text)

                self.status_var.set("✅ Trading signals generated")

            except Exception as e:
                messagebox.showerror("Error", f"Signal generation failed: {str(e)}")
                self.status_var.set("❌ Signal generation failed")

        threading.Thread(target=signals_thread, daemon=True).start()

    def create_price_chart(self):
        """Create price chart with technical indicators"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8),
                                          gridspec_kw={'height_ratios': [3, 1]})
            fig.patch.set_facecolor('#2b2b2b')

            prices = self.current_data['prices']
            volumes = self.current_data['volumes']

            # Create time axis
            time_axis = range(len(prices))

            # Price chart
            ax1.plot(time_axis, prices, color='#00ff88', linewidth=2, label='Price')

            # Add moving averages if enough data
            if len(prices) > 20:
                sma_20 = self.indicators.simple_moving_average(prices, 20)
                ax1.plot(time_axis[-len(sma_20):], sma_20, color='#ff6b6b',
                        linewidth=1, label='SMA(20)', alpha=0.8)

            if len(prices) > 50:
                sma_50 = self.indicators.simple_moving_average(prices, 50)
                ax1.plot(time_axis[-len(sma_50):], sma_50, color='#4ecdc4',
                        linewidth=1, label='SMA(50)', alpha=0.8)

            # Bollinger Bands
            if len(prices) > 20:
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                bb_time = time_axis[-len(upper_bb):]
                ax1.fill_between(bb_time, upper_bb, lower_bb, alpha=0.1, color='gray')
                ax1.plot(bb_time, upper_bb, color='gray', linewidth=1, alpha=0.5)
                ax1.plot(bb_time, lower_bb, color='gray', linewidth=1, alpha=0.5)

            ax1.set_title(f'{self.current_symbol.upper()} Price Chart',
                         color='white', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Price ($)', color='white')
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # Volume chart
            ax2.bar(time_axis, volumes, color='#45b7d1', alpha=0.6)
            ax2.set_title('Volume', color='white', fontsize=12)
            ax2.set_ylabel('Volume', color='white')
            ax2.set_xlabel('Time Period', color='white')
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Price chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Chart creation failed: {str(e)}")
            self.status_var.set("❌ Chart creation failed")

    def create_indicators_chart(self):
        """Create technical indicators chart"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.patch.set_facecolor('#2b2b2b')

            prices = self.current_data['prices']
            time_axis = range(len(prices))

            # RSI
            rsi = self.indicators.rsi(prices)
            rsi_time = time_axis[-len(rsi):]
            ax1.plot(rsi_time, rsi, color='#ff6b6b', linewidth=2)
            ax1.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Overbought')
            ax1.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Oversold')
            ax1.axhline(y=50, color='gray', linestyle='-', alpha=0.5)
            ax1.set_title('RSI (Relative Strength Index)', color='white', fontweight='bold')
            ax1.set_ylabel('RSI', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # MACD
            macd_line, signal_line, histogram = self.indicators.macd(prices)
            macd_time = time_axis[-len(macd_line):]
            ax2.plot(macd_time, macd_line, color='#00ff88', linewidth=2, label='MACD')
            ax2.plot(macd_time, signal_line, color='#ff6b6b', linewidth=2, label='Signal')
            ax2.bar(macd_time, histogram, color='gray', alpha=0.6, label='Histogram')
            ax2.set_title('MACD', color='white', fontweight='bold')
            ax2.set_ylabel('MACD', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            # Volume with OBV
            volumes = self.current_data['volumes']
            obv = self.indicators.obv(prices, volumes)
            obv_time = time_axis[-len(obv):]
            ax3.plot(obv_time, obv, color='#4ecdc4', linewidth=2)
            ax3.set_title('On-Balance Volume (OBV)', color='white', fontweight='bold')
            ax3.set_ylabel('OBV', color='white')
            ax3.grid(True, alpha=0.3)
            ax3.set_facecolor('#1e1e1e')
            ax3.tick_params(colors='white')

            # Price with Bollinger Bands
            ax4.plot(time_axis, prices, color='#00ff88', linewidth=2, label='Price')
            if len(prices) > 20:
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                bb_time = time_axis[-len(upper_bb):]
                ax4.plot(bb_time, upper_bb, color='red', linewidth=1, alpha=0.7, label='Upper BB')
                ax4.plot(bb_time, middle_bb, color='blue', linewidth=1, alpha=0.7, label='Middle BB')
                ax4.plot(bb_time, lower_bb, color='green', linewidth=1, alpha=0.7, label='Lower BB')
                ax4.fill_between(bb_time, upper_bb, lower_bb, alpha=0.1, color='gray')

            ax4.set_title('Bollinger Bands', color='white', fontweight='bold')
            ax4.set_ylabel('Price ($)', color='white')
            ax4.set_xlabel('Time Period', color='white')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.set_facecolor('#1e1e1e')
            ax4.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Technical indicators chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Indicators chart creation failed: {str(e)}")
            self.status_var.set("❌ Indicators chart creation failed")

    def create_signals_chart(self):
        """Create trading signals visualization"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Generate signals first
            features = self._prepare_ml_features()
            if features is None:
                messagebox.showwarning("Warning", "Insufficient data for signal analysis")
                return

            prices = self.current_data['prices']
            returns = np.diff(prices[-len(features)-1:])[:len(features)]
            target = (returns > 0).astype(int)

            # Train model
            split_idx = int(len(features) * 0.8)
            X_train, X_test = features[:split_idx], features[split_idx:]
            y_train, y_test = target[:split_idx], target[split_idx:]

            signal_model = LogisticRegression(learning_rate=0.1, max_iterations=500)
            signal_model.fit(X_train, y_train)

            signals = signal_model.generate_trading_signals(X_test)
            probabilities = signal_model.predict_proba(X_test)

            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8),
                                          gridspec_kw={'height_ratios': [3, 1]})
            fig.patch.set_facecolor('#2b2b2b')

            # Price chart with signals
            test_prices = prices[-len(signals):]
            time_axis = range(len(test_prices))

            ax1.plot(time_axis, test_prices, color='#00ff88', linewidth=2, label='Price')

            # Add signal markers
            buy_points = [i for i, sig in enumerate(signals) if sig == 1]
            sell_points = [i for i, sig in enumerate(signals) if sig == -1]

            if buy_points:
                ax1.scatter([time_axis[i] for i in buy_points],
                           [test_prices[i] for i in buy_points],
                           color='green', marker='^', s=100, label='Buy Signal', zorder=5)

            if sell_points:
                ax1.scatter([time_axis[i] for i in sell_points],
                           [test_prices[i] for i in sell_points],
                           color='red', marker='v', s=100, label='Sell Signal', zorder=5)

            ax1.set_title(f'{self.current_symbol.upper()} Trading Signals',
                         color='white', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Price ($)', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # Signal probabilities
            ax2.plot(time_axis, probabilities, color='#45b7d1', linewidth=2, label='Buy Probability')
            ax2.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, label='Buy Threshold')
            ax2.axhline(y=0.3, color='red', linestyle='--', alpha=0.7, label='Sell Threshold')
            ax2.axhline(y=0.5, color='gray', linestyle='-', alpha=0.5)
            ax2.set_title('Signal Probabilities', color='white', fontsize=12)
            ax2.set_ylabel('Probability', color='white')
            ax2.set_xlabel('Time Period', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Trading signals chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Signals chart creation failed: {str(e)}")
            self.status_var.set("❌ Signals chart creation failed")

    def _prepare_ml_features(self):
        """Prepare features for ML models"""
        if not self.current_data:
            return None

        prices = self.current_data['prices']
        volumes = self.current_data['volumes']

        if len(prices) < 50:
            return None

        try:
            # Calculate returns
            returns = np.diff(np.log(prices))

            # Technical indicators as features
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            rsi = self.indicators.rsi(prices)
            volume_sma = self.indicators.simple_moving_average(volumes, 10)

            # Align all features to same length
            min_len = min(len(sma_20), len(rsi), len(volume_sma))

            if min_len < 10:
                return None

            features = np.column_stack([
                sma_20[-min_len:],
                rsi[-min_len:],
                volume_sma[-min_len:],
                returns[-min_len:] if len(returns) >= min_len else np.zeros(min_len)
            ])

            # Normalize features
            features_norm, _, _ = self.matrix_ops.normalize(features)

            return features_norm

        except Exception as e:
            print(f"Error preparing features: {e}")
            return None

def main():
    """Main function to run the Bloomberg Terminal"""
    root = tk.Tk()
    app = CryptoBloombergTerminal(root)

    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1200 // 2)
    y = (root.winfo_screenheight() // 2) - (800 // 2)
    root.geometry(f"1200x800+{x}+{y}")

    # Set minimum size
    root.minsize(1000, 600)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Application terminated by user")

if __name__ == "__main__":
    main()
