"""
Crypto ML Analysis GUI Application
Desktop application using Tkinter for cryptocurrency market analysis
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

# Import our ML components
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from crypto_analysis.market_analyzer import CryptoMarketAnalyzer
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans
from ml_core.matrix_ops import MatrixOps

class CryptoMLGUI:
    """Main GUI Application for Crypto ML Analysis"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 Crypto ML Analysis - Pure NumPy Implementation")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Initialize ML components
        self.data_fetcher = CryptoDataFetcher()
        self.indicators = TechnicalIndicators()
        self.analyzer = CryptoMarketAnalyzer()
        self.matrix_ops = MatrixOps()
        
        # Data storage
        self.current_data = None
        self.current_symbol = None
        
        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        
    def setup_styles(self):
        """Setup custom styles for the application"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', 
                       font=('Arial', 16, 'bold'),
                       background='#2b2b2b',
                       foreground='#ffffff')
        
        style.configure('Header.TLabel',
                       font=('Arial', 12, 'bold'),
                       background='#2b2b2b',
                       foreground='#4CAF50')
        
        style.configure('Info.TLabel',
                       font=('Arial', 10),
                       background='#2b2b2b',
                       foreground='#ffffff')
        
        style.configure('Custom.TButton',
                       font=('Arial', 10, 'bold'),
                       padding=10)
        
    def create_widgets(self):
        """Create and layout all GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, 
                               text="🚀 Crypto ML Analysis Application",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame,
                                  text="💎 Pure NumPy Implementation - No ML Frameworks Required!",
                                  style='Info.TLabel')
        subtitle_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_data_tab()
        self.create_analysis_tab()
        self.create_ml_tab()
        self.create_signals_tab()
        self.create_charts_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Load cryptocurrency data to begin analysis")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              style='Info.TLabel', relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
    def create_data_tab(self):
        """Create data loading tab"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="📊 Data Loading")
        
        # Data loading section
        load_frame = ttk.LabelFrame(data_frame, text="Load Cryptocurrency Data", padding=20)
        load_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Cryptocurrency selection
        ttk.Label(load_frame, text="Select Cryptocurrency:", style='Header.TLabel').pack(anchor=tk.W)
        
        self.crypto_var = tk.StringVar(value="bitcoin")
        crypto_frame = ttk.Frame(load_frame)
        crypto_frame.pack(fill=tk.X, pady=5)
        
        cryptos = [("🟠 Bitcoin", "bitcoin"), ("🔵 Ethereum", "ethereum"), 
                  ("🟢 Cardano", "cardano"), ("🟣 Solana", "solana")]
        
        for text, value in cryptos:
            ttk.Radiobutton(crypto_frame, text=text, variable=self.crypto_var, 
                           value=value).pack(side=tk.LEFT, padx=10)
        
        # Time period selection
        ttk.Label(load_frame, text="Select Time Period:", style='Header.TLabel').pack(anchor=tk.W, pady=(20, 0))
        
        self.period_var = tk.StringVar(value="30")
        period_frame = ttk.Frame(load_frame)
        period_frame.pack(fill=tk.X, pady=5)
        
        periods = [("📅 7 days", "7"), ("📅 30 days", "30"), 
                  ("📅 90 days", "90"), ("📅 180 days", "180")]
        
        for text, value in periods:
            ttk.Radiobutton(period_frame, text=text, variable=self.period_var, 
                           value=value).pack(side=tk.LEFT, padx=10)
        
        # Load button
        ttk.Button(load_frame, text="🔄 Load Data", 
                  command=self.load_data, style='Custom.TButton').pack(pady=20)
        
        # Data info section
        info_frame = ttk.LabelFrame(data_frame, text="Data Information", padding=20)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.data_info = scrolledtext.ScrolledText(info_frame, height=15, 
                                                  bg='#1e1e1e', fg='#ffffff',
                                                  font=('Consolas', 10))
        self.data_info.pack(fill=tk.BOTH, expand=True)
        
    def create_analysis_tab(self):
        """Create technical analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="📈 Technical Analysis")
        
        # Control panel
        control_frame = ttk.LabelFrame(analysis_frame, text="Analysis Controls", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="📊 Run Technical Analysis", 
                  command=self.run_technical_analysis, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="🔄 Refresh Analysis", 
                  command=self.refresh_analysis, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        # Results display
        results_frame = ttk.LabelFrame(analysis_frame, text="Analysis Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.analysis_results = scrolledtext.ScrolledText(results_frame, height=20,
                                                         bg='#1e1e1e', fg='#ffffff',
                                                         font=('Consolas', 10))
        self.analysis_results.pack(fill=tk.BOTH, expand=True)
        
    def create_ml_tab(self):
        """Create ML prediction tab"""
        ml_frame = ttk.Frame(self.notebook)
        self.notebook.add(ml_frame, text="🤖 ML Prediction")
        
        # ML controls
        ml_control_frame = ttk.LabelFrame(ml_frame, text="ML Model Controls", padding=10)
        ml_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(ml_control_frame, text="🔮 Price Prediction", 
                  command=self.run_price_prediction, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(ml_control_frame, text="🔄 Market Regimes", 
                  command=self.run_market_regimes, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        # ML results
        ml_results_frame = ttk.LabelFrame(ml_frame, text="ML Results", padding=10)
        ml_results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.ml_results = scrolledtext.ScrolledText(ml_results_frame, height=20,
                                                   bg='#1e1e1e', fg='#ffffff',
                                                   font=('Consolas', 10))
        self.ml_results.pack(fill=tk.BOTH, expand=True)
        
    def create_signals_tab(self):
        """Create trading signals tab"""
        signals_frame = ttk.Frame(self.notebook)
        self.notebook.add(signals_frame, text="🎯 Trading Signals")
        
        # Signals controls
        signals_control_frame = ttk.LabelFrame(signals_frame, text="Signal Generation", padding=10)
        signals_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(signals_control_frame, text="🎯 Generate Signals", 
                  command=self.generate_trading_signals, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        # Signal parameters
        param_frame = ttk.Frame(signals_control_frame)
        param_frame.pack(side=tk.LEFT, padx=20)
        
        ttk.Label(param_frame, text="Buy Threshold:").pack(side=tk.LEFT)
        self.buy_threshold = tk.DoubleVar(value=0.7)
        ttk.Scale(param_frame, from_=0.5, to=0.9, variable=self.buy_threshold, 
                 orient=tk.HORIZONTAL, length=100).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(param_frame, text="Sell Threshold:").pack(side=tk.LEFT, padx=(20, 0))
        self.sell_threshold = tk.DoubleVar(value=0.3)
        ttk.Scale(param_frame, from_=0.1, to=0.5, variable=self.sell_threshold, 
                 orient=tk.HORIZONTAL, length=100).pack(side=tk.LEFT, padx=5)
        
        # Signals results
        signals_results_frame = ttk.LabelFrame(signals_frame, text="Trading Signals Results", padding=10)
        signals_results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.signals_results = scrolledtext.ScrolledText(signals_results_frame, height=20,
                                                        bg='#1e1e1e', fg='#ffffff',
                                                        font=('Consolas', 10))
        self.signals_results.pack(fill=tk.BOTH, expand=True)
        
    def create_charts_tab(self):
        """Create charts and visualization tab"""
        charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(charts_frame, text="📊 Charts")
        
        # Chart controls
        chart_control_frame = ttk.LabelFrame(charts_frame, text="Chart Controls", padding=10)
        chart_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(chart_control_frame, text="📈 Price Chart", 
                  command=self.create_price_chart, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(chart_control_frame, text="📊 Technical Indicators", 
                  command=self.create_indicators_chart, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(chart_control_frame, text="🎯 Trading Signals", 
                  command=self.create_signals_chart, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        
        # Chart display area
        self.chart_frame = ttk.Frame(charts_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def load_data(self):
        """Load cryptocurrency data in background thread"""
        def load_data_thread():
            try:
                self.status_var.set("Loading data...")
                self.root.update()
                
                symbol = self.crypto_var.get()
                days = int(self.period_var.get())
                
                # Load data
                self.current_data = self.data_fetcher.fetch_price_history(symbol, days)
                self.current_symbol = symbol
                
                # Update data info
                self.update_data_info()
                
                self.status_var.set(f"✅ Data loaded: {symbol.upper()} ({days} days)")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load data: {str(e)}")
                self.status_var.set("❌ Error loading data")
        
        # Run in background thread
        threading.Thread(target=load_data_thread, daemon=True).start()
        
    def update_data_info(self):
        """Update data information display"""
        if not self.current_data:
            return
            
        info_text = f"""
🚀 CRYPTOCURRENCY DATA LOADED
{'='*50}

📊 Asset: {self.current_symbol.upper()}
📅 Data Points: {len(self.current_data['prices'])}
💰 Current Price: ${self.current_data['prices'][-1]:.2f}
📈 Highest Price: ${np.max(self.current_data['prices']):.2f}
📉 Lowest Price: ${np.min(self.current_data['prices']):.2f}
📊 Price Range: ${np.max(self.current_data['prices']) - np.min(self.current_data['prices']):.2f}

📊 Volume Statistics:
   Average Volume: {np.mean(self.current_data['volumes']):,.0f}
   Max Volume: {np.max(self.current_data['volumes']):,.0f}
   Min Volume: {np.min(self.current_data['volumes']):,.0f}

📈 Price Changes:
   24h Change: {((self.current_data['prices'][-1] - self.current_data['prices'][-24]) / self.current_data['prices'][-24] * 100):+.2f}%
   7d Change: {((self.current_data['prices'][-1] - self.current_data['prices'][-min(168, len(self.current_data['prices'])-1)]) / self.current_data['prices'][-min(168, len(self.current_data['prices'])-1)] * 100):+.2f}%

⚠️ Data Source: {'Synthetic (Demo)' if 'synthetic' in self.current_data else 'Real API Data'}

✅ Data ready for analysis!
"""
        
        self.data_info.delete(1.0, tk.END)
        self.data_info.insert(tk.END, info_text)
        
    def run_technical_analysis(self):
        """Run technical analysis"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return
            
        def analysis_thread():
            try:
                self.status_var.set("Running technical analysis...")
                
                prices = self.current_data['prices']
                volumes = self.current_data['volumes']
                
                # Calculate indicators
                sma_20 = self.indicators.simple_moving_average(prices, 20)
                sma_50 = self.indicators.simple_moving_average(prices, 50)
                ema_12 = self.indicators.exponential_moving_average(prices, 12)
                rsi = self.indicators.rsi(prices)
                macd_line, signal_line, histogram = self.indicators.macd(prices)
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                
                # Format results
                current_price = prices[-1]
                
                results_text = f"""
🚀 TECHNICAL ANALYSIS RESULTS
{'='*60}

💰 CURRENT MARKET STATUS:
   Current Price: ${current_price:.2f}
   
📈 MOVING AVERAGES:
   SMA(20): ${sma_20[-1]:.2f} {'📈 Above' if current_price > sma_20[-1] else '📉 Below'}
   SMA(50): ${sma_50[-1]:.2f} {'📈 Above' if current_price > sma_50[-1] else '📉 Below'}
   EMA(12): ${ema_12[-1]:.2f} {'📈 Above' if current_price > ema_12[-1] else '📉 Below'}
   
   Trend Analysis:
   - SMA(20) vs SMA(50): {'🟢 Bullish' if sma_20[-1] > sma_50[-1] else '🔴 Bearish'}
   - Price vs SMA(20): {'🟢 Bullish' if current_price > sma_20[-1] else '🔴 Bearish'}

🎯 MOMENTUM INDICATORS:
   RSI: {rsi[-1]:.1f} {'🔴 Overbought' if rsi[-1] > 70 else '🟢 Oversold' if rsi[-1] < 30 else '🟡 Neutral'}
   MACD Signal: {'🟢 Bullish' if histogram[-1] > 0 else '🔴 Bearish'}
   MACD Line: {macd_line[-1]:.4f}
   Signal Line: {signal_line[-1]:.4f}
   Histogram: {histogram[-1]:.4f}

💨 VOLATILITY ANALYSIS:
   Bollinger Bands:
   - Upper Band: ${upper_bb[-1]:.2f}
   - Middle Band: ${middle_bb[-1]:.2f}
   - Lower Band: ${lower_bb[-1]:.2f}
   - Position: {((current_price - lower_bb[-1]) / (upper_bb[-1] - lower_bb[-1]) * 100):.1f}%
   
   Band Analysis: {'🔴 Near Upper (Overbought)' if current_price > upper_bb[-1] * 0.95 else '🟢 Near Lower (Oversold)' if current_price < lower_bb[-1] * 1.05 else '🟡 Middle Range'}

📊 VOLUME ANALYSIS:
   Current Volume: {volumes[-1]:,.0f}
   Average Volume: {np.mean(volumes):,.0f}
   Volume Ratio: {volumes[-1] / np.mean(volumes):.2f}x
   Volume Trend: {'🔴 High Volume' if volumes[-1] > np.mean(volumes) * 1.5 else '🟢 Low Volume' if volumes[-1] < np.mean(volumes) * 0.5 else '🟡 Normal Volume'}

💡 SUMMARY:
   Overall Trend: {'🟢 BULLISH' if (current_price > sma_20[-1] and rsi[-1] < 70 and histogram[-1] > 0) else '🔴 BEARISH' if (current_price < sma_20[-1] and rsi[-1] > 30 and histogram[-1] < 0) else '🟡 NEUTRAL'}
   
   Key Levels:
   - Resistance: ${upper_bb[-1]:.2f} (Upper Bollinger Band)
   - Support: ${lower_bb[-1]:.2f} (Lower Bollinger Band)
   
⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                
                self.analysis_results.delete(1.0, tk.END)
                self.analysis_results.insert(tk.END, results_text)
                
                self.status_var.set("✅ Technical analysis completed")
                
            except Exception as e:
                messagebox.showerror("Error", f"Analysis failed: {str(e)}")
                self.status_var.set("❌ Analysis failed")
        
        threading.Thread(target=analysis_thread, daemon=True).start()
        
    def refresh_analysis(self):
        """Refresh technical analysis"""
        self.run_technical_analysis()
        
    def run_price_prediction(self):
        """Run ML price prediction"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return
            
        def prediction_thread():
            try:
                self.status_var.set("Training ML model for price prediction...")
                
                # Prepare features
                features = self._prepare_ml_features()
                if features is None:
                    messagebox.showwarning("Warning", "Insufficient data for ML analysis")
                    return
                
                prices = self.current_data['prices']
                target = np.diff(prices[-len(features)-1:])[:len(features)]
                
                # Split data
                split_idx = int(len(features) * 0.8)
                X_train, X_test = features[:split_idx], features[split_idx:]
                y_train, y_test = target[:split_idx], target[split_idx:]
                
                # Train model
                model = LinearRegression(learning_rate=0.01, max_iterations=1000)
                model.fit(X_train, y_train)
                
                # Make predictions
                train_score = model.score(X_train, y_train)
                test_score = model.score(X_test, y_test)
                
                # Future predictions
                future_pred = model.predict_trend(features[-5:], periods=5)
                current_price = prices[-1]
                
                results_text = f"""
🚀 ML PRICE PREDICTION RESULTS
{'='*60}

🤖 MODEL PERFORMANCE:
   Training R² Score: {train_score:.4f}
   Testing R² Score: {test_score:.4f}
   Model Quality: {'🟢 Excellent' if test_score > 0.8 else '🟡 Good' if test_score > 0.6 else '🔴 Fair'}

💰 CURRENT STATUS:
   Current Price: ${current_price:.2f}

🔮 PRICE PREDICTIONS (Next 5 Periods):
"""
                
                for i, pred in enumerate(future_pred, 1):
                    predicted_price = current_price + pred
                    change_pct = (pred / current_price) * 100
                    direction = "📈" if pred > 0 else "📉"
                    results_text += f"   Period +{i}: ${predicted_price:.2f} ({change_pct:+.2f}%) {direction}\n"
                
                # Feature importance
                importance = model.get_feature_importance()
                if importance is not None:
                    results_text += f"""
🎯 FEATURE IMPORTANCE:
   Technical Indicators Contribution:
   - SMA Influence: {importance[0]:.3f}
   - RSI Influence: {importance[1]:.3f}
   - Volume Influence: {importance[2]:.3f}
   - Price Changes: {importance[3]:.3f}

💡 PREDICTION SUMMARY:
   Short-term Outlook: {'🟢 BULLISH' if np.mean(future_pred[:3]) > 0 else '🔴 BEARISH'}
   Average Predicted Change: ${np.mean(future_pred):.2f} ({(np.mean(future_pred)/current_price*100):+.2f}%)
   Prediction Confidence: {'🟢 High' if test_score > 0.7 else '🟡 Medium' if test_score > 0.5 else '🔴 Low'}

⚠️ Prediction completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⚠️ This is for educational purposes only. Not financial advice.
"""
                
                self.ml_results.delete(1.0, tk.END)
                self.ml_results.insert(tk.END, results_text)
                
                self.status_var.set("✅ ML prediction completed")
                
            except Exception as e:
                messagebox.showerror("Error", f"ML prediction failed: {str(e)}")
                self.status_var.set("❌ ML prediction failed")
        
        threading.Thread(target=prediction_thread, daemon=True).start()

    def run_market_regimes(self):
        """Run market regime analysis"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def regime_thread():
            try:
                self.status_var.set("Analyzing market regimes...")

                prices = self.current_data['prices']
                volumes = self.current_data['volumes']

                # Calculate features for regime identification
                returns = np.diff(np.log(prices))
                volatility = np.array([np.std(returns[max(0, i-24):i+1]) for i in range(len(returns))])
                volume_normalized = (volumes[1:] - np.mean(volumes)) / np.std(volumes)

                # Align arrays
                min_len = min(len(returns), len(volatility), len(volume_normalized))

                regime_features = np.column_stack([
                    returns[-min_len:],
                    volatility[-min_len:],
                    volume_normalized[-min_len:]
                ])

                # Normalize features
                regime_features = (regime_features - np.mean(regime_features, axis=0)) / (np.std(regime_features, axis=0) + 1e-8)

                # Apply K-means clustering
                kmeans = KMeans(n_clusters=3, random_state=42)
                labels = kmeans.fit_predict(regime_features)

                # Analyze regimes
                regime_names = ['🟢 Low Volatility', '🟡 Normal Market', '🔴 High Volatility']
                current_regime = labels[-1]

                results_text = f"""
🚀 MARKET REGIME ANALYSIS
{'='*60}

🔄 REGIME IDENTIFICATION:
   Total Regimes Identified: {kmeans.n_clusters}
   Analysis Period: {min_len} data points

📊 REGIME DISTRIBUTION:
"""

                for i in range(kmeans.n_clusters):
                    regime_points = np.sum(labels == i)
                    percentage = (regime_points / len(labels)) * 100
                    results_text += f"   Regime {i} ({regime_names[i]}): {regime_points} periods ({percentage:.1f}%)\n"

                results_text += f"""
🎯 CURRENT MARKET REGIME:
   Current Regime: {current_regime} ({regime_names[current_regime]})

📊 REGIME CHARACTERISTICS:
   Returns Level: {kmeans.centroids[current_regime][0]:.4f}
   Volatility Level: {kmeans.centroids[current_regime][1]:.4f}
   Volume Level: {kmeans.centroids[current_regime][2]:.4f}

💡 REGIME INTERPRETATION:
"""

                if current_regime == 0:
                    results_text += """   🟢 LOW VOLATILITY REGIME:
   - Stable market conditions
   - Lower risk environment
   - Good for trend-following strategies
   - Conservative position sizing recommended
"""
                elif current_regime == 1:
                    results_text += """   🟡 NORMAL MARKET REGIME:
   - Balanced market conditions
   - Standard risk-reward environment
   - Normal trading strategies applicable
   - Regular position sizing appropriate
"""
                else:
                    results_text += """   🔴 HIGH VOLATILITY REGIME:
   - Increased market volatility
   - Higher risk and opportunity
   - Consider risk management strategies
   - Reduced position sizing recommended
"""

                # Recent regime history
                results_text += f"""
📊 RECENT REGIME HISTORY:
"""
                recent_regimes = labels[-10:] if len(labels) >= 10 else labels
                for i, regime in enumerate(recent_regimes):
                    regime_name = regime_names[regime].split()[1]
                    results_text += f"   Period -{len(recent_regimes)-i}: {regime_name}\n"

                results_text += f"""
⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.ml_results.delete(1.0, tk.END)
                self.ml_results.insert(tk.END, results_text)

                self.status_var.set("✅ Market regime analysis completed")

            except Exception as e:
                messagebox.showerror("Error", f"Regime analysis failed: {str(e)}")
                self.status_var.set("❌ Regime analysis failed")

        threading.Thread(target=regime_thread, daemon=True).start()

    def generate_trading_signals(self):
        """Generate trading signals"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        def signals_thread():
            try:
                self.status_var.set("Generating trading signals...")

                features = self._prepare_ml_features()
                if features is None:
                    messagebox.showwarning("Warning", "Insufficient data for signal analysis")
                    return

                prices = self.current_data['prices']
                returns = np.diff(prices[-len(features)-1:])[:len(features)]
                target = (returns > 0).astype(int)

                # Split data
                split_idx = int(len(features) * 0.8)
                X_train, X_test = features[:split_idx], features[split_idx:]
                y_train, y_test = target[:split_idx], target[split_idx:]

                # Train Logistic Regression
                signal_model = LogisticRegression(learning_rate=0.1, max_iterations=1000)
                signal_model.fit(X_train, y_train)

                # Generate signals
                buy_thresh = self.buy_threshold.get()
                sell_thresh = self.sell_threshold.get()

                signals = signal_model.generate_trading_signals(X_test,
                                                              buy_threshold=buy_thresh,
                                                              sell_threshold=sell_thresh)
                probabilities = signal_model.predict_proba(X_test)
                accuracy = signal_model.score(X_test, y_test)

                # Count signals
                buy_signals = np.sum(signals == 1)
                sell_signals = np.sum(signals == -1)
                hold_signals = np.sum(signals == 0)

                # Current signal
                current_signal = signals[-1] if len(signals) > 0 else 0
                current_prob = probabilities[-1] if len(probabilities) > 0 else 0.5

                signal_text = "🟢 BUY" if current_signal == 1 else "🔴 SELL" if current_signal == -1 else "🟡 HOLD"

                results_text = f"""
🚀 TRADING SIGNALS ANALYSIS
{'='*60}

🤖 MODEL PERFORMANCE:
   Signal Accuracy: {accuracy:.4f}
   Model Quality: {'🟢 Excellent' if accuracy > 0.8 else '🟡 Good' if accuracy > 0.6 else '🔴 Fair'}

⚙️ SIGNAL PARAMETERS:
   Buy Threshold: {buy_thresh:.2f}
   Sell Threshold: {sell_thresh:.2f}

📊 SIGNAL DISTRIBUTION:
   📈 Buy Signals: {buy_signals} ({buy_signals/len(signals)*100:.1f}%)
   📉 Sell Signals: {sell_signals} ({sell_signals/len(signals)*100:.1f}%)
   ⏸️  Hold Signals: {hold_signals} ({hold_signals/len(signals)*100:.1f}%)

🎯 CURRENT TRADING SIGNAL:
   Signal: {signal_text}
   Confidence: {current_prob:.3f}

💡 RECOMMENDATION:
"""

                if current_signal == 1:
                    results_text += """   🟢 BUY SIGNAL ACTIVE
   - ML model predicts price increase
   - Consider entering long position
   - Monitor for confirmation signals
   - Use appropriate position sizing
"""
                elif current_signal == -1:
                    results_text += """   🔴 SELL SIGNAL ACTIVE
   - ML model predicts price decrease
   - Consider exiting long positions
   - Monitor for trend reversal
   - Consider risk management
"""
                else:
                    results_text += """   🟡 HOLD SIGNAL ACTIVE
   - Uncertain market direction
   - Maintain current positions
   - Wait for clearer signals
   - Monitor market conditions
"""

                # Recent signal history
                results_text += f"""
📊 RECENT SIGNAL HISTORY:
"""
                recent_signals = signals[-10:] if len(signals) >= 10 else signals
                for i, sig in enumerate(recent_signals):
                    sig_text = "BUY" if sig == 1 else "SELL" if sig == -1 else "HOLD"
                    results_text += f"   Period -{len(recent_signals)-i}: {sig_text}\n"

                results_text += f"""
⚠️ IMPORTANT DISCLAIMERS:
   - Signals are based on historical patterns
   - Past performance doesn't guarantee future results
   - Always use proper risk management
   - Consider multiple analysis methods
   - This is for educational purposes only

⚠️ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.signals_results.delete(1.0, tk.END)
                self.signals_results.insert(tk.END, results_text)

                self.status_var.set("✅ Trading signals generated")

            except Exception as e:
                messagebox.showerror("Error", f"Signal generation failed: {str(e)}")
                self.status_var.set("❌ Signal generation failed")

        threading.Thread(target=signals_thread, daemon=True).start()

    def create_price_chart(self):
        """Create price chart with technical indicators"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8),
                                          gridspec_kw={'height_ratios': [3, 1]})
            fig.patch.set_facecolor('#2b2b2b')

            prices = self.current_data['prices']
            volumes = self.current_data['volumes']

            # Create time axis
            time_axis = range(len(prices))

            # Price chart
            ax1.plot(time_axis, prices, color='#00ff88', linewidth=2, label='Price')

            # Add moving averages if enough data
            if len(prices) > 20:
                sma_20 = self.indicators.simple_moving_average(prices, 20)
                ax1.plot(time_axis[-len(sma_20):], sma_20, color='#ff6b6b',
                        linewidth=1, label='SMA(20)', alpha=0.8)

            if len(prices) > 50:
                sma_50 = self.indicators.simple_moving_average(prices, 50)
                ax1.plot(time_axis[-len(sma_50):], sma_50, color='#4ecdc4',
                        linewidth=1, label='SMA(50)', alpha=0.8)

            # Bollinger Bands
            if len(prices) > 20:
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                bb_time = time_axis[-len(upper_bb):]
                ax1.fill_between(bb_time, upper_bb, lower_bb, alpha=0.1, color='gray')
                ax1.plot(bb_time, upper_bb, color='gray', linewidth=1, alpha=0.5)
                ax1.plot(bb_time, lower_bb, color='gray', linewidth=1, alpha=0.5)

            ax1.set_title(f'{self.current_symbol.upper()} Price Chart',
                         color='white', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Price ($)', color='white')
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # Volume chart
            ax2.bar(time_axis, volumes, color='#45b7d1', alpha=0.6)
            ax2.set_title('Volume', color='white', fontsize=12)
            ax2.set_ylabel('Volume', color='white')
            ax2.set_xlabel('Time Period', color='white')
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Price chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Chart creation failed: {str(e)}")
            self.status_var.set("❌ Chart creation failed")

    def create_indicators_chart(self):
        """Create technical indicators chart"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.patch.set_facecolor('#2b2b2b')

            prices = self.current_data['prices']
            time_axis = range(len(prices))

            # RSI
            rsi = self.indicators.rsi(prices)
            rsi_time = time_axis[-len(rsi):]
            ax1.plot(rsi_time, rsi, color='#ff6b6b', linewidth=2)
            ax1.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Overbought')
            ax1.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Oversold')
            ax1.axhline(y=50, color='gray', linestyle='-', alpha=0.5)
            ax1.set_title('RSI (Relative Strength Index)', color='white', fontweight='bold')
            ax1.set_ylabel('RSI', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # MACD
            macd_line, signal_line, histogram = self.indicators.macd(prices)
            macd_time = time_axis[-len(macd_line):]
            ax2.plot(macd_time, macd_line, color='#00ff88', linewidth=2, label='MACD')
            ax2.plot(macd_time, signal_line, color='#ff6b6b', linewidth=2, label='Signal')
            ax2.bar(macd_time, histogram, color='gray', alpha=0.6, label='Histogram')
            ax2.set_title('MACD', color='white', fontweight='bold')
            ax2.set_ylabel('MACD', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            # Volume with OBV
            volumes = self.current_data['volumes']
            obv = self.indicators.obv(prices, volumes)
            obv_time = time_axis[-len(obv):]
            ax3.plot(obv_time, obv, color='#4ecdc4', linewidth=2)
            ax3.set_title('On-Balance Volume (OBV)', color='white', fontweight='bold')
            ax3.set_ylabel('OBV', color='white')
            ax3.grid(True, alpha=0.3)
            ax3.set_facecolor('#1e1e1e')
            ax3.tick_params(colors='white')

            # Price with Bollinger Bands
            ax4.plot(time_axis, prices, color='#00ff88', linewidth=2, label='Price')
            if len(prices) > 20:
                upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
                bb_time = time_axis[-len(upper_bb):]
                ax4.plot(bb_time, upper_bb, color='red', linewidth=1, alpha=0.7, label='Upper BB')
                ax4.plot(bb_time, middle_bb, color='blue', linewidth=1, alpha=0.7, label='Middle BB')
                ax4.plot(bb_time, lower_bb, color='green', linewidth=1, alpha=0.7, label='Lower BB')
                ax4.fill_between(bb_time, upper_bb, lower_bb, alpha=0.1, color='gray')

            ax4.set_title('Bollinger Bands', color='white', fontweight='bold')
            ax4.set_ylabel('Price ($)', color='white')
            ax4.set_xlabel('Time Period', color='white')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.set_facecolor('#1e1e1e')
            ax4.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Technical indicators chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Indicators chart creation failed: {str(e)}")
            self.status_var.set("❌ Indicators chart creation failed")

    def create_signals_chart(self):
        """Create trading signals visualization"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load cryptocurrency data first!")
            return

        try:
            # Generate signals first
            features = self._prepare_ml_features()
            if features is None:
                messagebox.showwarning("Warning", "Insufficient data for signal analysis")
                return

            prices = self.current_data['prices']
            returns = np.diff(prices[-len(features)-1:])[:len(features)]
            target = (returns > 0).astype(int)

            # Train model
            split_idx = int(len(features) * 0.8)
            X_train, X_test = features[:split_idx], features[split_idx:]
            y_train, y_test = target[:split_idx], target[split_idx:]

            signal_model = LogisticRegression(learning_rate=0.1, max_iterations=500)
            signal_model.fit(X_train, y_train)

            signals = signal_model.generate_trading_signals(X_test)
            probabilities = signal_model.predict_proba(X_test)

            # Clear previous chart
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # Create figure
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8),
                                          gridspec_kw={'height_ratios': [3, 1]})
            fig.patch.set_facecolor('#2b2b2b')

            # Price chart with signals
            test_prices = prices[-len(signals):]
            time_axis = range(len(test_prices))

            ax1.plot(time_axis, test_prices, color='#00ff88', linewidth=2, label='Price')

            # Add signal markers
            buy_points = [i for i, sig in enumerate(signals) if sig == 1]
            sell_points = [i for i, sig in enumerate(signals) if sig == -1]

            if buy_points:
                ax1.scatter([time_axis[i] for i in buy_points],
                           [test_prices[i] for i in buy_points],
                           color='green', marker='^', s=100, label='Buy Signal', zorder=5)

            if sell_points:
                ax1.scatter([time_axis[i] for i in sell_points],
                           [test_prices[i] for i in sell_points],
                           color='red', marker='v', s=100, label='Sell Signal', zorder=5)

            ax1.set_title(f'{self.current_symbol.upper()} Trading Signals',
                         color='white', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Price ($)', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#1e1e1e')
            ax1.tick_params(colors='white')

            # Signal probabilities
            ax2.plot(time_axis, probabilities, color='#45b7d1', linewidth=2, label='Buy Probability')
            ax2.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, label='Buy Threshold')
            ax2.axhline(y=0.3, color='red', linestyle='--', alpha=0.7, label='Sell Threshold')
            ax2.axhline(y=0.5, color='gray', linestyle='-', alpha=0.5)
            ax2.set_title('Signal Probabilities', color='white', fontsize=12)
            ax2.set_ylabel('Probability', color='white')
            ax2.set_xlabel('Time Period', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#1e1e1e')
            ax2.tick_params(colors='white')

            plt.tight_layout()

            # Embed chart in tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            self.status_var.set("✅ Trading signals chart created")

        except Exception as e:
            messagebox.showerror("Error", f"Signals chart creation failed: {str(e)}")
            self.status_var.set("❌ Signals chart creation failed")

    def _prepare_ml_features(self):
        """Prepare features for ML models"""
        if not self.current_data:
            return None

        prices = self.current_data['prices']
        volumes = self.current_data['volumes']

        if len(prices) < 50:
            return None

        try:
            # Calculate returns
            returns = np.diff(np.log(prices))

            # Technical indicators as features
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            rsi = self.indicators.rsi(prices)
            volume_sma = self.indicators.simple_moving_average(volumes, 10)

            # Align all features to same length
            min_len = min(len(sma_20), len(rsi), len(volume_sma))

            if min_len < 10:
                return None

            features = np.column_stack([
                sma_20[-min_len:],
                rsi[-min_len:],
                volume_sma[-min_len:],
                returns[-min_len:] if len(returns) >= min_len else np.zeros(min_len)
            ])

            # Normalize features
            features_norm, _, _ = self.matrix_ops.normalize(features)

            return features_norm

        except Exception as e:
            print(f"Error preparing features: {e}")
            return None

def main():
    """Main function to run the GUI application"""
    root = tk.Tk()
    app = CryptoMLGUI(root)

    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1200 // 2)
    y = (root.winfo_screenheight() // 2) - (800 // 2)
    root.geometry(f"1200x800+{x}+{y}")

    # Set minimum size
    root.minsize(1000, 600)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Application terminated by user")

if __name__ == "__main__":
    main()
