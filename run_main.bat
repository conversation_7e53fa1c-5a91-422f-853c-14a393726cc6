@echo off
echo 🚀 Running Crypto ML Main Analysis...
echo =====================================

REM Set Python path
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

REM Check if Python exists
if not exist "%PYTHON_PATH%" (
    echo ❌ Python not found at %PYTHON_PATH%
    echo Please check your Python installation
    pause
    exit /b 1
)

REM Run the main analysis
echo 📊 Starting main analysis with Python at: %PYTHON_PATH%
echo.
"%PYTHON_PATH%" main.py

echo.
echo ✅ Analysis completed!
pause
