"""
K-Means Clustering - Pure NumPy Implementation
For crypto market regime identification
"""
import numpy as np

class KMeans:
    """K-Means clustering algorithm"""
    
    def __init__(self, n_clusters=3, max_iterations=100, tolerance=1e-4, random_state=None):
        self.n_clusters = n_clusters
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.random_state = random_state
        self.centroids = None
        self.labels = None
        self.inertia = None
        
    def fit(self, X):
        """
        Fit K-means clustering
        Args:
            X: Feature matrix (n_samples, n_features)
        """
        if self.random_state:
            np.random.seed(self.random_state)
        
        n_samples, n_features = X.shape
        
        # Initialize centroids randomly
        self.centroids = self._initialize_centroids(X)
        
        for iteration in range(self.max_iterations):
            # Assign points to closest centroid
            distances = self._calculate_distances(X)
            new_labels = np.argmin(distances, axis=1)
            
            # Update centroids
            new_centroids = np.zeros((self.n_clusters, n_features))
            for k in range(self.n_clusters):
                if np.sum(new_labels == k) > 0:
                    new_centroids[k] = np.mean(X[new_labels == k], axis=0)
                else:
                    new_centroids[k] = self.centroids[k]
            
            # Check for convergence
            if np.allclose(self.centroids, new_centroids, atol=self.tolerance):
                break
            
            self.centroids = new_centroids
            self.labels = new_labels
        
        # Calculate inertia (within-cluster sum of squares)
        self.inertia = self._calculate_inertia(X)
        
        return self
    
    def _initialize_centroids(self, X):
        """Initialize centroids using K-means++ method"""
        n_samples, n_features = X.shape
        centroids = np.zeros((self.n_clusters, n_features))
        
        # Choose first centroid randomly
        centroids[0] = X[np.random.randint(n_samples)]
        
        # Choose remaining centroids
        for k in range(1, self.n_clusters):
            # Calculate distances to nearest centroid
            distances = np.array([min([np.linalg.norm(x - c)**2 for c in centroids[:k]]) for x in X])
            
            # Choose next centroid with probability proportional to squared distance
            probabilities = distances / distances.sum()
            cumulative_probabilities = probabilities.cumsum()
            r = np.random.rand()
            
            for i, p in enumerate(cumulative_probabilities):
                if r < p:
                    centroids[k] = X[i]
                    break
        
        return centroids
    
    def _calculate_distances(self, X):
        """Calculate distances from each point to each centroid"""
        distances = np.zeros((X.shape[0], self.n_clusters))
        
        for k, centroid in enumerate(self.centroids):
            distances[:, k] = np.linalg.norm(X - centroid, axis=1)
        
        return distances
    
    def _calculate_inertia(self, X):
        """Calculate within-cluster sum of squares"""
        inertia = 0
        for k in range(self.n_clusters):
            cluster_points = X[self.labels == k]
            if len(cluster_points) > 0:
                inertia += np.sum((cluster_points - self.centroids[k])**2)
        return inertia
    
    def predict(self, X):
        """Predict cluster labels for new data"""
        distances = self._calculate_distances(X)
        return np.argmin(distances, axis=1)
    
    def fit_predict(self, X):
        """Fit the model and predict cluster labels"""
        self.fit(X)
        return self.labels
    
    def identify_market_regimes(self, price_data, volume_data, volatility_data):
        """
        Identify crypto market regimes based on price, volume, and volatility
        Args:
            price_data: Price returns or normalized prices
            volume_data: Trading volume data
            volatility_data: Volatility measurements
        Returns:
            regime_labels: Market regime for each time period
            regime_descriptions: Description of each regime
        """
        # Combine features
        features = np.column_stack([price_data, volume_data, volatility_data])
        
        # Fit clustering
        self.fit(features)
        
        # Analyze centroids to describe regimes
        regime_descriptions = []
        for k in range(self.n_clusters):
            centroid = self.centroids[k]
            price_level = "High" if centroid[0] > 0.5 else "Low" if centroid[0] < -0.5 else "Normal"
            volume_level = "High" if centroid[1] > 0.5 else "Low" if centroid[1] < -0.5 else "Normal"
            volatility_level = "High" if centroid[2] > 0.5 else "Low" if centroid[2] < -0.5 else "Normal"
            
            regime_descriptions.append(f"{price_level} Price, {volume_level} Volume, {volatility_level} Volatility")
        
        return self.labels, regime_descriptions
    
    def elbow_method(self, X, max_k=10):
        """
        Find optimal number of clusters using elbow method
        Args:
            X: Feature matrix
            max_k: Maximum number of clusters to test
        Returns:
            inertias: List of inertias for each k
        """
        inertias = []
        
        for k in range(1, max_k + 1):
            kmeans = KMeans(n_clusters=k, random_state=self.random_state)
            kmeans.fit(X)
            inertias.append(kmeans.inertia)
        
        return inertias
