"""
Crypto ML Real-time Analysis with REAL Live Data
Uses real cryptocurrency data from multiple exchanges
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.animation import FuncAnimation

# Import our components
from real_data_fetcher import RealTimeCryptoFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.matrix_ops import MatrixOps

class CryptoRealTimeLive:
    """Real-time Crypto Analysis with Live Data"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🔴 LIVE Crypto ML Analysis - Real Market Data")
        self.root.geometry("1500x1000")
        self.root.configure(bg='#0a0a0a')
        
        # Initialize components
        self.data_fetcher = RealTimeCryptoFetcher()
        self.indicators = TechnicalIndicators()
        self.matrix_ops = MatrixOps()
        
        # Live data storage
        self.live_data = {
            'bitcoin': {'prices': [], 'volumes': [], 'timestamps': []},
            'ethereum': {'prices': [], 'volumes': [], 'timestamps': []},
            'cardano': {'prices': [], 'volumes': [], 'timestamps': []},
            'solana': {'prices': [], 'volumes': [], 'timestamps': []}
        }
        
        self.current_symbol = 'bitcoin'
        self.is_live_active = False
        self.update_interval = 10  # seconds
        self.live_thread = None
        self.websocket_active = False
        
        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        
        # Start with initial data load
        self.load_initial_data()
        
    def setup_styles(self):
        """Setup dark theme styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Live theme styles
        style.configure('Live.TLabel', 
                       font=('Arial', 20, 'bold'),
                       background='#0a0a0a',
                       foreground='#ff0000')
        
        style.configure('Price.TLabel',
                       font=('Arial', 28, 'bold'),
                       background='#0a0a0a',
                       foreground='#00ff00')
        
        style.configure('Change.TLabel',
                       font=('Arial', 16),
                       background='#0a0a0a')
        
        style.configure('LiveButton.TButton',
                       font=('Arial', 12, 'bold'),
                       padding=15)
        
    def create_widgets(self):
        """Create live trading interface"""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Live indicator
        live_label = ttk.Label(header_frame, text="🔴 LIVE", style='Live.TLabel')
        live_label.pack(side=tk.LEFT)
        
        title_label = ttk.Label(header_frame, 
                               text="Real-time Crypto Market Analysis",
                               font=('Arial', 16, 'bold'),
                               background='#0a0a0a',
                               foreground='#ffffff')
        title_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # Status
        self.live_status = ttk.Label(header_frame,
                                    text="⏸️ OFFLINE",
                                    font=('Arial', 14, 'bold'),
                                    background='#0a0a0a',
                                    foreground='#ff4444')
        self.live_status.pack(side=tk.RIGHT)
        
        # Control panel
        self.create_live_controls()
        
        # Main content - 4 panels
        content_frame = ttk.Frame(self.root)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Top row: Price displays
        top_frame = ttk.Frame(content_frame)
        top_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.create_price_panels(top_frame)
        
        # Bottom row: Chart and analysis
        bottom_frame = ttk.Frame(content_frame)
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        self.create_chart_panel(bottom_frame)
        self.create_analysis_panel(bottom_frame)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Click 'Start Live Feed' to begin real-time analysis")
        status_bar = ttk.Label(self.root, textvariable=self.status_var,
                              font=('Arial', 10),
                              background='#0a0a0a',
                              foreground='#888888',
                              relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
    def create_live_controls(self):
        """Create live trading controls"""
        control_frame = ttk.LabelFrame(self.root, text="🔴 Live Trading Controls", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Left: Symbol selection
        left_frame = ttk.Frame(control_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(left_frame, text="Primary Symbol:", 
                 font=('Arial', 11, 'bold'),
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        
        self.symbol_var = tk.StringVar(value="bitcoin")
        symbol_combo = ttk.Combobox(left_frame, textvariable=self.symbol_var,
                                   values=["bitcoin", "ethereum", "cardano", "solana"],
                                   state="readonly", width=12)
        symbol_combo.pack(side=tk.LEFT, padx=(5, 20))
        symbol_combo.bind('<<ComboboxSelected>>', self.on_symbol_change)
        
        # Update interval
        ttk.Label(left_frame, text="Update:", 
                 font=('Arial', 11, 'bold'),
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        
        self.interval_var = tk.StringVar(value="10")
        interval_combo = ttk.Combobox(left_frame, textvariable=self.interval_var,
                                     values=["5", "10", "15", "30", "60"],
                                     state="readonly", width=6)
        interval_combo.pack(side=tk.LEFT, padx=(5, 5))
        ttk.Label(left_frame, text="sec", 
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        
        # Right: Control buttons
        right_frame = ttk.Frame(control_frame)
        right_frame.pack(side=tk.RIGHT)
        
        self.start_live_btn = ttk.Button(right_frame, text="🔴 Start Live Feed",
                                        command=self.start_live_feed, 
                                        style='LiveButton.TButton')
        self.start_live_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_live_btn = ttk.Button(right_frame, text="⏹️ Stop Feed",
                                       command=self.stop_live_feed,
                                       style='LiveButton.TButton',
                                       state=tk.DISABLED)
        self.stop_live_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(right_frame, text="📊 Refresh All",
                  command=self.refresh_all_data,
                  style='LiveButton.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(right_frame, text="🚀 WebSocket",
                  command=self.toggle_websocket,
                  style='LiveButton.TButton').pack(side=tk.LEFT, padx=5)
        
    def create_price_panels(self, parent):
        """Create live price display panels"""
        symbols = ['bitcoin', 'ethereum', 'cardano', 'solana']
        self.price_panels = {}
        
        for i, symbol in enumerate(symbols):
            panel = ttk.LabelFrame(parent, text=f"💰 {symbol.upper()}", padding=10)
            panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2)
            
            # Price display
            price_label = ttk.Label(panel, text="$0.00", style='Price.TLabel')
            price_label.pack()
            
            # Change display
            change_label = ttk.Label(panel, text="0.00%", style='Change.TLabel')
            change_label.pack()
            
            # Additional info
            info_frame = ttk.Frame(panel)
            info_frame.pack(fill=tk.X, pady=5)
            
            volume_label = ttk.Label(info_frame, text="Vol: --",
                                   font=('Arial', 9),
                                   background='#0a0a0a',
                                   foreground='#888888')
            volume_label.pack()
            
            source_label = ttk.Label(info_frame, text="Source: --",
                                   font=('Arial', 8),
                                   background='#0a0a0a',
                                   foreground='#666666')
            source_label.pack()
            
            self.price_panels[symbol] = {
                'price': price_label,
                'change': change_label,
                'volume': volume_label,
                'source': source_label
            }
    
    def create_chart_panel(self, parent):
        """Create live chart panel"""
        chart_frame = ttk.LabelFrame(parent, text="📈 Live Price Chart", padding=5)
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Create matplotlib figure
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 8),
                                                      gridspec_kw={'height_ratios': [3, 1]})
        self.fig.patch.set_facecolor('#0a0a0a')
        
        # Style the axes
        for ax in [self.ax1, self.ax2]:
            ax.set_facecolor('#1a1a1a')
            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.2, color='gray')
        
        # Initialize empty plots
        self.price_line, = self.ax1.plot([], [], color='#00ff88', linewidth=2, label='Live Price')
        self.sma_line, = self.ax1.plot([], [], color='#ff6b6b', linewidth=1, alpha=0.8, label='SMA(20)')
        
        self.ax1.set_title('Live Price Movement', color='white', fontweight='bold')
        self.ax1.set_ylabel('Price ($)', color='white')
        self.ax1.legend(loc='upper left')
        
        self.ax2.set_title('Volume', color='white')
        self.ax2.set_ylabel('Volume', color='white')
        self.ax2.set_xlabel('Time', color='white')
        
        plt.tight_layout()
        
        # Embed chart
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def create_analysis_panel(self, parent):
        """Create live analysis panel"""
        analysis_frame = ttk.LabelFrame(parent, text="🤖 Live Analysis", padding=5)
        analysis_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Quick metrics
        metrics_frame = ttk.Frame(analysis_frame)
        metrics_frame.pack(fill=tk.X, pady=(0, 5))
        
        # RSI
        rsi_frame = ttk.Frame(metrics_frame)
        rsi_frame.pack(fill=tk.X, pady=1)
        ttk.Label(rsi_frame, text="RSI:",
                 font=('Arial', 10, 'bold'),
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        self.rsi_label = ttk.Label(rsi_frame, text="--",
                                  background='#0a0a0a',
                                  foreground='#00ff88')
        self.rsi_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # MACD
        macd_frame = ttk.Frame(metrics_frame)
        macd_frame.pack(fill=tk.X, pady=1)
        ttk.Label(macd_frame, text="MACD:",
                 font=('Arial', 10, 'bold'),
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        self.macd_label = ttk.Label(macd_frame, text="--",
                                   background='#0a0a0a',
                                   foreground='#00ff88')
        self.macd_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Signal
        signal_frame = ttk.Frame(metrics_frame)
        signal_frame.pack(fill=tk.X, pady=1)
        ttk.Label(signal_frame, text="Signal:",
                 font=('Arial', 10, 'bold'),
                 background='#0a0a0a',
                 foreground='#ffffff').pack(side=tk.LEFT)
        self.signal_label = ttk.Label(signal_frame, text="--",
                                     background='#0a0a0a',
                                     foreground='#ffff00')
        self.signal_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Detailed analysis
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, height=30,
                                                      bg='#1a1a1a', fg='#ffffff',
                                                      font=('Consolas', 9),
                                                      wrap=tk.WORD)
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
        
    def load_initial_data(self):
        """Load initial historical data"""
        def load_thread():
            try:
                self.status_var.set("📊 Loading initial market data...")
                
                for symbol in ['bitcoin', 'ethereum', 'cardano', 'solana']:
                    # Get historical data
                    historical = self.data_fetcher.get_historical_klines(symbol, '1m', 100)
                    
                    if historical.get('success'):
                        data = historical['data']
                        
                        # Extract prices, volumes, timestamps
                        prices = [point['close'] for point in data]
                        volumes = [point['volume'] for point in data]
                        timestamps = [point['timestamp'] for point in data]
                        
                        # Store in live_data
                        self.live_data[symbol] = {
                            'prices': prices,
                            'volumes': volumes,
                            'timestamps': timestamps
                        }
                        
                        print(f"✅ Loaded {len(prices)} historical points for {symbol}")
                    
                    time.sleep(0.2)  # Rate limiting
                
                # Update displays
                self.root.after(0, self.update_all_displays)
                self.root.after(0, lambda: self.status_var.set("✅ Initial data loaded - Ready for live feed"))
                
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"❌ Initial load error: {e}"))
        
        threading.Thread(target=load_thread, daemon=True).start()
        
    def start_live_feed(self):
        """Start live data feed"""
        if not self.is_live_active:
            self.is_live_active = True
            self.update_interval = int(self.interval_var.get())
            
            # Update UI
            self.start_live_btn.config(state=tk.DISABLED)
            self.stop_live_btn.config(state=tk.NORMAL)
            self.live_status.config(text="🔴 LIVE", foreground='#00ff00')
            
            # Start live thread
            self.live_thread = threading.Thread(target=self.live_update_loop, daemon=True)
            self.live_thread.start()
            
            self.status_var.set(f"🔴 LIVE feed started - Updates every {self.update_interval}s")
            
    def stop_live_feed(self):
        """Stop live data feed"""
        self.is_live_active = False
        
        # Update UI
        self.start_live_btn.config(state=tk.NORMAL)
        self.stop_live_btn.config(state=tk.DISABLED)
        self.live_status.config(text="⏸️ OFFLINE", foreground='#ff4444')
        
        self.status_var.set("⏸️ Live feed stopped")
        
    def live_update_loop(self):
        """Main live update loop"""
        while self.is_live_active:
            try:
                # Fetch live data for all symbols
                symbols = ['bitcoin', 'ethereum', 'cardano', 'solana']
                live_prices = self.data_fetcher.get_multiple_prices(symbols)
                
                # Update data storage
                current_time = datetime.now()
                
                for symbol, price_data in live_prices.items():
                    if price_data.get('success'):
                        # Add to live data
                        self.live_data[symbol]['prices'].append(price_data['price'])
                        self.live_data[symbol]['volumes'].append(price_data.get('volume_24h', 0))
                        self.live_data[symbol]['timestamps'].append(current_time)
                        
                        # Keep only last 200 points for performance
                        if len(self.live_data[symbol]['prices']) > 200:
                            self.live_data[symbol]['prices'] = self.live_data[symbol]['prices'][-200:]
                            self.live_data[symbol]['volumes'] = self.live_data[symbol]['volumes'][-200:]
                            self.live_data[symbol]['timestamps'] = self.live_data[symbol]['timestamps'][-200:]
                
                # Update displays
                self.root.after(0, self.update_all_displays)
                
                # Wait for next update
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"Live update error: {e}")
                time.sleep(5)  # Wait before retry
                
    def update_all_displays(self):
        """Update all display components"""
        try:
            # Update price panels
            for symbol in ['bitcoin', 'ethereum', 'cardano', 'solana']:
                self.update_price_panel(symbol)
            
            # Update chart for current symbol
            self.update_live_chart()
            
            # Update analysis
            self.update_live_analysis()
            
        except Exception as e:
            print(f"Display update error: {e}")
            
    def update_price_panel(self, symbol):
        """Update individual price panel"""
        try:
            data = self.live_data[symbol]
            
            if len(data['prices']) > 0:
                current_price = data['prices'][-1]
                
                # Update price
                self.price_panels[symbol]['price'].config(text=f"${current_price:,.2f}")
                
                # Calculate change
                if len(data['prices']) > 1:
                    prev_price = data['prices'][-2]
                    change = current_price - prev_price
                    change_pct = (change / prev_price) * 100
                    
                    change_text = f"{change:+.2f} ({change_pct:+.2f}%)"
                    change_color = '#00ff88' if change >= 0 else '#ff4444'
                    
                    self.price_panels[symbol]['change'].config(text=change_text, foreground=change_color)
                
                # Update volume
                if len(data['volumes']) > 0:
                    volume = data['volumes'][-1]
                    self.price_panels[symbol]['volume'].config(text=f"Vol: {volume:,.0f}")
                
                # Update source
                self.price_panels[symbol]['source'].config(text="Source: Live API")
                
        except Exception as e:
            print(f"Price panel update error for {symbol}: {e}")
            
    def update_live_chart(self):
        """Update live chart"""
        try:
            symbol = self.symbol_var.get()
            data = self.live_data[symbol]
            
            if len(data['prices']) < 2:
                return
            
            prices = np.array(data['prices'])
            
            # Update price line
            x_data = list(range(len(prices)))
            self.price_line.set_data(x_data, prices)
            
            # Update SMA if enough data
            if len(prices) >= 20:
                sma_data = self.indicators.simple_moving_average(prices, 20)
                sma_x = list(range(len(prices) - len(sma_data), len(prices)))
                self.sma_line.set_data(sma_x, sma_data)
            
            # Update axes
            self.ax1.relim()
            self.ax1.autoscale_view()
            self.ax1.set_title(f'{symbol.upper()} Live Price Movement', color='white', fontweight='bold')
            
            # Update volume
            self.ax2.clear()
            if len(data['volumes']) > 0:
                self.ax2.bar(x_data, data['volumes'], color='#45b7d1', alpha=0.6)
            
            self.ax2.set_title('Volume', color='white')
            self.ax2.set_ylabel('Volume', color='white')
            self.ax2.set_xlabel('Time', color='white')
            self.ax2.tick_params(colors='white')
            self.ax2.grid(True, alpha=0.2, color='gray')
            self.ax2.set_facecolor('#1a1a1a')
            
            # Redraw
            self.canvas.draw()
            
        except Exception as e:
            print(f"Chart update error: {e}")
            
    def update_live_analysis(self):
        """Update live analysis"""
        try:
            symbol = self.symbol_var.get()
            data = self.live_data[symbol]
            
            if len(data['prices']) < 20:
                return
            
            prices = np.array(data['prices'])
            
            # Calculate indicators
            rsi = self.indicators.rsi(prices)
            macd_line, signal_line, histogram = self.indicators.macd(prices)
            
            # Update quick metrics
            if len(rsi) > 0:
                rsi_current = rsi[-1]
                rsi_status = "🔴 Overbought" if rsi_current > 70 else "🟢 Oversold" if rsi_current < 30 else "🟡 Neutral"
                self.rsi_label.config(text=f"{rsi_current:.1f} {rsi_status}")
            
            if len(histogram) > 0:
                macd_signal = "🟢 Bullish" if histogram[-1] > 0 else "🔴 Bearish"
                self.macd_label.config(text=macd_signal)
            
            # Generate signal
            if len(rsi) > 0 and len(histogram) > 0:
                if rsi[-1] < 30 and histogram[-1] > 0:
                    signal = "🟢 STRONG BUY"
                elif rsi[-1] > 70 and histogram[-1] < 0:
                    signal = "🔴 STRONG SELL"
                elif histogram[-1] > 0:
                    signal = "🟢 BUY"
                elif histogram[-1] < 0:
                    signal = "🔴 SELL"
                else:
                    signal = "🟡 HOLD"
                
                self.signal_label.config(text=signal)
            
            # Detailed analysis
            analysis_text = f"""
🔴 LIVE MARKET ANALYSIS - {symbol.upper()}
{'='*45}

💰 CURRENT STATUS:
   Price: ${prices[-1]:,.2f}
   24h Change: {((prices[-1] - prices[0]) / prices[0] * 100):+.2f}%
   
📈 TECHNICAL INDICATORS:
   RSI: {rsi[-1]:.1f} ({rsi_status})
   MACD: {macd_signal}
   MACD Line: {macd_line[-1]:.6f}
   Signal Line: {signal_line[-1]:.6f}
   Histogram: {histogram[-1]:.6f}

🎯 TRADING SIGNAL: {signal}

📊 MARKET MOMENTUM:
   Short-term: {'🟢 Bullish' if prices[-1] > prices[-5] else '🔴 Bearish'}
   Medium-term: {'🟢 Bullish' if prices[-1] > prices[-20] else '🔴 Bearish'}
   
💨 VOLATILITY:
   Recent Vol: {np.std(prices[-10:]):.2f}
   
📊 VOLUME ANALYSIS:
   Current: {data['volumes'][-1]:,.0f}
   Average: {np.mean(data['volumes']):,.0f}
   
⏰ Last Update: {datetime.now().strftime('%H:%M:%S')}
🔴 Status: {'LIVE FEED ACTIVE' if self.is_live_active else 'OFFLINE'}

⚠️ LIVE TRADING DISCLAIMER:
This is real market data for educational purposes.
Not financial advice. Trade at your own risk.
"""
            
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, analysis_text)
            
        except Exception as e:
            print(f"Analysis update error: {e}")
            
    def on_symbol_change(self, event=None):
        """Handle symbol change"""
        self.current_symbol = self.symbol_var.get()
        self.update_live_chart()
        self.update_live_analysis()
        
    def refresh_all_data(self):
        """Manually refresh all data"""
        def refresh_thread():
            try:
                self.status_var.set("🔄 Refreshing all market data...")
                
                symbols = ['bitcoin', 'ethereum', 'cardano', 'solana']
                live_prices = self.data_fetcher.get_multiple_prices(symbols)
                
                current_time = datetime.now()
                
                for symbol, price_data in live_prices.items():
                    if price_data.get('success'):
                        self.live_data[symbol]['prices'].append(price_data['price'])
                        self.live_data[symbol]['volumes'].append(price_data.get('volume_24h', 0))
                        self.live_data[symbol]['timestamps'].append(current_time)
                
                self.root.after(0, self.update_all_displays)
                self.root.after(0, lambda: self.status_var.set("✅ All data refreshed"))
                
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"❌ Refresh error: {e}"))
        
        threading.Thread(target=refresh_thread, daemon=True).start()
        
    def toggle_websocket(self):
        """Toggle WebSocket connection"""
        if not self.websocket_active:
            # Start WebSocket
            def websocket_callback(data):
                symbol = data['symbol']
                if symbol in self.live_data:
                    self.live_data[symbol]['prices'].append(data['price'])
                    self.live_data[symbol]['volumes'].append(data.get('volume', 0))
                    self.live_data[symbol]['timestamps'].append(datetime.now())
                    
                    # Update display
                    self.root.after(0, self.update_all_displays)
            
            success = self.data_fetcher.start_websocket_stream(self.current_symbol, websocket_callback)
            
            if success:
                self.websocket_active = True
                self.status_var.set("🚀 WebSocket connected for real-time updates")
            else:
                self.status_var.set("❌ WebSocket connection failed")
        else:
            # Stop WebSocket
            self.data_fetcher.stop_websocket_stream(self.current_symbol)
            self.websocket_active = False
            self.status_var.set("⏸️ WebSocket disconnected")

def main():
    """Main function"""
    root = tk.Tk()
    app = CryptoRealTimeLive(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1500 // 2)
    y = (root.winfo_screenheight() // 2) - (1000 // 2)
    root.geometry(f"1500x1000+{x}+{y}")
    
    root.minsize(1200, 800)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Live application terminated")

if __name__ == "__main__":
    main()
