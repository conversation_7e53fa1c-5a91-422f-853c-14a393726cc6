"""
Simple Demo of Crypto ML System
Pure NumPy implementation for cryptocurrency analysis
"""
import numpy as np
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans

def main():
    print("🚀 Crypto ML System Demo")
    print("=" * 40)
    
    # 1. Generate synthetic crypto data
    print("📊 Generating synthetic Bitcoin data...")
    fetcher = CryptoDataFetcher()
    data = fetcher._generate_synthetic_data('bitcoin', 30)
    
    prices = data['prices']
    volumes = data['volumes']
    
    print(f"✅ Generated {len(prices)} price points")
    print(f"   Price range: ${prices.min():.0f} - ${prices.max():.0f}")
    
    # 2. Technical Analysis
    print("\n📈 Technical Analysis:")
    indicators = TechnicalIndicators()
    
    # Calculate indicators
    sma_20 = indicators.simple_moving_average(prices, 20)
    ema_12 = indicators.exponential_moving_average(prices, 12)
    rsi = indicators.rsi(prices)
    
    print(f"   Current Price: ${prices[-1]:.2f}")
    print(f"   SMA(20): ${sma_20[-1]:.2f}")
    print(f"   EMA(12): ${ema_12[-1]:.2f}")
    print(f"   RSI: {rsi[-1]:.1f} ({'Overbought' if rsi[-1] > 70 else 'Oversold' if rsi[-1] < 30 else 'Neutral'})")
    
    # MACD
    macd_line, signal_line, histogram = indicators.macd(prices)
    macd_signal = "Bullish" if histogram[-1] > 0 else "Bearish"
    print(f"   MACD Signal: {macd_signal}")
    
    # 3. ML Price Prediction
    print("\n🤖 ML Price Prediction:")
    
    # Prepare simple features
    window = 50
    features = np.column_stack([
        sma_20[-window:],
        rsi[-window:],
        np.diff(prices[-window-1:])  # Price changes
    ])
    
    # Normalize features
    features = (features - np.mean(features, axis=0)) / (np.std(features, axis=0) + 1e-8)
    
    # Target: next price change (ensure same length as features)
    target = np.diff(prices[-window-1:])[:len(features)]

    # Train Linear Regression
    model = LinearRegression(learning_rate=0.01, max_iterations=500)

    # Use 80% for training
    split = int(len(features) * 0.8)
    X_train, X_test = features[:split], features[split:]
    y_train, y_test = target[:split], target[split:]
    
    model.fit(X_train, y_train)
    
    # Make predictions
    predictions = model.predict(X_test)
    score = model.score(X_test, y_test)
    
    print(f"   Model R² Score: {score:.3f}")
    print(f"   Next predicted change: ${predictions[-1]:.2f}")
    print(f"   Predicted next price: ${prices[-1] + predictions[-1]:.2f}")
    
    # 4. Trading Signals
    print("\n🎯 Trading Signal Generation:")
    
    # Binary classification: price up (1) or down (0)
    binary_target = (target > 0).astype(int)
    
    # Train Logistic Regression
    signal_model = LogisticRegression(learning_rate=0.1, max_iterations=500)
    signal_model.fit(X_train, binary_target[:split])
    
    # Generate signals
    signals = signal_model.generate_trading_signals(X_test)
    accuracy = signal_model.score(X_test, binary_target[split:])
    
    print(f"   Signal Accuracy: {accuracy:.3f}")
    
    # Count signals
    buy_signals = np.sum(signals == 1)
    sell_signals = np.sum(signals == -1)
    hold_signals = np.sum(signals == 0)
    
    print(f"   Recent signals - Buy: {buy_signals}, Sell: {sell_signals}, Hold: {hold_signals}")
    
    current_signal = signals[-1] if len(signals) > 0 else 0
    signal_text = "BUY" if current_signal == 1 else "SELL" if current_signal == -1 else "HOLD"
    print(f"   Current Signal: {signal_text}")
    
    # 5. Market Regime Analysis
    print("\n🔄 Market Regime Analysis:")
    
    # Features for clustering: returns, volatility, volume
    returns = np.diff(np.log(prices))
    volatility = np.array([np.std(returns[max(0, i-10):i+1]) for i in range(len(returns))])
    volume_norm = (volumes[1:] - np.mean(volumes)) / np.std(volumes)
    
    # Align arrays
    min_len = min(len(returns), len(volatility), len(volume_norm))
    
    regime_features = np.column_stack([
        returns[-min_len:],
        volatility[-min_len:],
        volume_norm[-min_len:]
    ])
    
    # Normalize
    regime_features = (regime_features - np.mean(regime_features, axis=0)) / (np.std(regime_features, axis=0) + 1e-8)
    
    # K-means clustering
    kmeans = KMeans(n_clusters=3, random_state=42)
    labels = kmeans.fit_predict(regime_features)
    
    # Analyze regimes
    regime_names = ['Low Volatility', 'Normal Market', 'High Volatility']
    current_regime = labels[-1]
    
    print(f"   Identified {kmeans.n_clusters} market regimes")
    for i in range(kmeans.n_clusters):
        count = np.sum(labels == i)
        percentage = (count / len(labels)) * 100
        print(f"   Regime {i} ({regime_names[i]}): {count} periods ({percentage:.1f}%)")
    
    print(f"   Current Market Regime: {current_regime} ({regime_names[current_regime]})")
    
    # 6. Summary
    print(f"\n📋 ANALYSIS SUMMARY:")
    print(f"   Current Price: ${prices[-1]:.2f}")
    print(f"   Trend: {macd_signal}")
    print(f"   RSI Status: {'Overbought' if rsi[-1] > 70 else 'Oversold' if rsi[-1] < 30 else 'Neutral'}")
    print(f"   Trading Signal: {signal_text}")
    print(f"   Market Regime: {regime_names[current_regime]}")
    print(f"   Model Accuracy: {score:.3f}")
    
    print(f"\n🎉 Demo completed successfully!")
    print(f"💡 This system uses pure NumPy - no ML frameworks required!")

if __name__ == "__main__":
    main()
