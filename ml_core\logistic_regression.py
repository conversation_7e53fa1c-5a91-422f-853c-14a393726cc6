"""
Logistic Regression - Pure NumPy Implementation
For crypto buy/sell signal classification
"""
import numpy as np
from .matrix_ops import MatrixOps

class LogisticRegression:
    """Logistic Regression for binary classification"""
    
    def __init__(self, learning_rate=0.01, max_iterations=1000, tolerance=1e-6):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.weights = None
        self.bias = None
        self.cost_history = []
    
    def _sigmoid(self, z):
        """Sigmoid activation function"""
        # Clip z to prevent overflow
        z = np.clip(z, -500, 500)
        return 1 / (1 + np.exp(-z))
    
    def fit(self, X, y):
        """
        Train the logistic regression model
        Args:
            X: Feature matrix (n_samples, n_features)
            y: Binary target values (n_samples,)
        """
        n_samples, n_features = X.shape
        
        # Initialize weights and bias
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        
        # Gradient descent
        for i in range(self.max_iterations):
            # Forward pass
            linear_pred = np.dot(X, self.weights) + self.bias
            y_pred = self._sigmoid(linear_pred)
            
            # Calculate cost (log-likelihood)
            cost = self._compute_cost(y, y_pred)
            self.cost_history.append(cost)
            
            # Calculate gradients
            dw = (1/n_samples) * np.dot(X.T, (y_pred - y))
            db = (1/n_samples) * np.sum(y_pred - y)
            
            # Update parameters
            self.weights -= self.learning_rate * dw
            self.bias -= self.learning_rate * db
            
            # Check for convergence
            if i > 0 and abs(self.cost_history[-2] - self.cost_history[-1]) < self.tolerance:
                break
    
    def _compute_cost(self, y_true, y_pred):
        """Compute logistic regression cost"""
        # Add small epsilon to prevent log(0)
        epsilon = 1e-15
        y_pred = np.clip(y_pred, epsilon, 1 - epsilon)
        
        cost = -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
        return cost
    
    def predict_proba(self, X):
        """Predict class probabilities"""
        linear_pred = np.dot(X, self.weights) + self.bias
        return self._sigmoid(linear_pred)
    
    def predict(self, X, threshold=0.5):
        """Make binary predictions"""
        probabilities = self.predict_proba(X)
        return (probabilities >= threshold).astype(int)
    
    def score(self, X, y):
        """Calculate accuracy score"""
        predictions = self.predict(X)
        return np.mean(predictions == y)
    
    def generate_trading_signals(self, X, buy_threshold=0.7, sell_threshold=0.3):
        """
        Generate trading signals for crypto
        Args:
            X: Feature matrix
            buy_threshold: Probability threshold for buy signal
            sell_threshold: Probability threshold for sell signal
        Returns:
            signals: 1 for buy, -1 for sell, 0 for hold
        """
        probabilities = self.predict_proba(X)
        signals = np.zeros(len(probabilities))
        
        signals[probabilities >= buy_threshold] = 1   # Buy signal
        signals[probabilities <= sell_threshold] = -1  # Sell signal
        
        return signals
    
    def get_feature_importance(self):
        """Get feature importance based on weights magnitude"""
        if self.weights is None:
            return None
        return np.abs(self.weights) / np.sum(np.abs(self.weights))
    
    def precision_recall(self, X, y):
        """Calculate precision and recall"""
        predictions = self.predict(X)
        
        # True positives, false positives, false negatives
        tp = np.sum((predictions == 1) & (y == 1))
        fp = np.sum((predictions == 1) & (y == 0))
        fn = np.sum((predictions == 0) & (y == 1))
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        
        return precision, recall
