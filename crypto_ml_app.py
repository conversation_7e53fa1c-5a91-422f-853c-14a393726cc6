"""
Crypto ML Analysis Application
Interactive application for cryptocurrency market analysis using pure NumPy
"""
import os
import sys
import time
import numpy as np
from datetime import datetime

# Import our ML components
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from crypto_analysis.market_analyzer import CryptoMarketAnalyzer
from ml_core.linear_regression import LinearRegression
from ml_core.logistic_regression import LogisticRegression
from ml_core.kmeans import KMeans
from ml_core.matrix_ops import MatrixOps

class CryptoMLApp:
    """Main Crypto ML Analysis Application"""
    
    def __init__(self):
        self.data_fetcher = CryptoDataFetcher()
        self.indicators = TechnicalIndicators()
        self.analyzer = CryptoMarketAnalyzer()
        self.matrix_ops = MatrixOps()
        self.current_data = None
        self.current_symbol = None
        
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Print application header"""
        print("=" * 70)
        print("🚀 CRYPTO ML ANALYSIS APPLICATION")
        print("💎 Pure NumPy Implementation - No ML Frameworks Required!")
        print("=" * 70)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if self.current_symbol:
            print(f"📊 Current Asset: {self.current_symbol.upper()}")
        print("=" * 70)
    
    def print_menu(self):
        """Print main menu"""
        print("\n🎯 MAIN MENU:")
        print("1. 📊 Load Cryptocurrency Data")
        print("2. 📈 Technical Analysis")
        print("3. 🤖 ML Price Prediction")
        print("4. 🎯 Trading Signal Analysis")
        print("5. 🔄 Market Regime Analysis")
        print("6. 📋 Complete Analysis Report")
        print("7. ⚙️  Settings & Configuration")
        print("8. ❓ Help & Documentation")
        print("9. 🚪 Exit Application")
        print("-" * 50)
    
    def wait_for_input(self, message="Press Enter to continue..."):
        """Wait for user input"""
        input(f"\n{message}")
    
    def get_user_choice(self):
        """Get user menu choice"""
        try:
            choice = input("👉 Select option (1-9): ").strip()
            return choice
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            sys.exit(0)
    
    def load_crypto_data(self):
        """Load cryptocurrency data"""
        self.clear_screen()
        self.print_header()
        
        print("\n📊 LOAD CRYPTOCURRENCY DATA")
        print("-" * 40)
        
        # Available cryptocurrencies
        available_cryptos = {
            '1': 'bitcoin',
            '2': 'ethereum', 
            '3': 'cardano',
            '4': 'solana',
            '5': 'custom'
        }
        
        print("Available Cryptocurrencies:")
        print("1. 🟠 Bitcoin (BTC)")
        print("2. 🔵 Ethereum (ETH)")
        print("3. 🟢 Cardano (ADA)")
        print("4. 🟣 Solana (SOL)")
        print("5. ⚪ Custom Symbol")
        
        crypto_choice = input("\n👉 Select cryptocurrency (1-5): ").strip()
        
        if crypto_choice in available_cryptos:
            if crypto_choice == '5':
                symbol = input("Enter custom symbol (e.g., bitcoin): ").strip().lower()
            else:
                symbol = available_cryptos[crypto_choice]
        else:
            print("❌ Invalid choice. Using Bitcoin as default.")
            symbol = 'bitcoin'
        
        # Get time period
        print("\nTime Period Options:")
        print("1. 📅 7 days")
        print("2. 📅 30 days") 
        print("3. 📅 90 days")
        print("4. 📅 Custom days")
        
        period_choice = input("\n👉 Select time period (1-4): ").strip()
        
        period_map = {'1': 7, '2': 30, '3': 90}
        
        if period_choice in period_map:
            days = period_map[period_choice]
        elif period_choice == '4':
            try:
                days = int(input("Enter number of days (1-365): "))
                days = max(1, min(365, days))
            except ValueError:
                days = 30
        else:
            days = 30
        
        print(f"\n🔄 Loading {symbol.upper()} data for {days} days...")
        
        try:
            self.current_data = self.data_fetcher.fetch_price_history(symbol, days)
            self.current_symbol = symbol
            
            if 'synthetic' in self.current_data:
                print("⚠️  Using synthetic data for demonstration")
            else:
                print("✅ Real market data loaded successfully")
            
            prices = self.current_data['prices']
            print(f"✅ Loaded {len(prices)} data points")
            print(f"📊 Price range: ${prices.min():.2f} - ${prices.max():.2f}")
            print(f"💰 Current price: ${prices[-1]:.2f}")
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            print("🔄 Generating synthetic data for demonstration...")
            self.current_data = self.data_fetcher._generate_synthetic_data(symbol, days)
            self.current_symbol = symbol
        
        self.wait_for_input()
    
    def technical_analysis(self):
        """Perform technical analysis"""
        if not self.current_data:
            print("❌ No data loaded. Please load cryptocurrency data first.")
            self.wait_for_input()
            return
        
        self.clear_screen()
        self.print_header()
        
        print(f"\n📈 TECHNICAL ANALYSIS - {self.current_symbol.upper()}")
        print("-" * 50)
        
        prices = self.current_data['prices']
        volumes = self.current_data['volumes']
        
        print("🔄 Calculating technical indicators...")
        
        try:
            # Moving Averages
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            sma_50 = self.indicators.simple_moving_average(prices, 50)
            ema_12 = self.indicators.exponential_moving_average(prices, 12)
            
            # Momentum Indicators
            rsi = self.indicators.rsi(prices)
            macd_line, signal_line, histogram = self.indicators.macd(prices)
            
            # Volatility Indicators
            upper_bb, middle_bb, lower_bb = self.indicators.bollinger_bands(prices)
            
            # Volume Indicators
            if 'closes' in self.current_data:
                obv = self.indicators.obv(self.current_data['closes'], volumes)
            else:
                obv = self.indicators.obv(prices, volumes)
            
            print("\n📊 TECHNICAL INDICATORS RESULTS:")
            print("=" * 50)
            
            # Current values
            current_price = prices[-1]
            print(f"💰 Current Price: ${current_price:.2f}")
            
            if len(sma_20) > 0:
                print(f"📈 SMA(20): ${sma_20[-1]:.2f}")
                trend_sma20 = "↗️ Bullish" if current_price > sma_20[-1] else "↘️ Bearish"
                print(f"   Trend vs SMA(20): {trend_sma20}")
            
            if len(sma_50) > 0:
                print(f"📈 SMA(50): ${sma_50[-1]:.2f}")
            
            if len(ema_12) > 0:
                print(f"📈 EMA(12): ${ema_12[-1]:.2f}")
            
            print(f"\n🎯 MOMENTUM INDICATORS:")
            if len(rsi) > 0:
                rsi_current = rsi[-1]
                rsi_status = "🔴 Overbought" if rsi_current > 70 else "🟢 Oversold" if rsi_current < 30 else "🟡 Neutral"
                print(f"📊 RSI: {rsi_current:.1f} ({rsi_status})")
            
            if len(histogram) > 0:
                macd_signal = "🟢 Bullish" if histogram[-1] > 0 else "🔴 Bearish"
                print(f"📊 MACD Signal: {macd_signal}")
            
            print(f"\n💨 VOLATILITY INDICATORS:")
            if len(upper_bb) > 0:
                bb_position = (current_price - lower_bb[-1]) / (upper_bb[-1] - lower_bb[-1])
                bb_status = "🔴 Near Upper Band" if bb_position > 0.8 else "🟢 Near Lower Band" if bb_position < 0.2 else "🟡 Middle Range"
                print(f"📊 Bollinger Bands Position: {bb_position:.2f} ({bb_status})")
            
            print(f"\n📊 VOLUME ANALYSIS:")
            current_volume = volumes[-1]
            avg_volume = np.mean(volumes)
            volume_ratio = current_volume / avg_volume
            volume_status = "🔴 High Volume" if volume_ratio > 1.5 else "🟢 Low Volume" if volume_ratio < 0.5 else "🟡 Normal Volume"
            print(f"📊 Volume Ratio: {volume_ratio:.2f}x ({volume_status})")
            
            if len(obv) > 0:
                obv_trend = "🟢 Bullish" if obv[-1] > obv[-10] else "🔴 Bearish"
                print(f"📊 OBV Trend: {obv_trend}")
            
        except Exception as e:
            print(f"❌ Error in technical analysis: {e}")
        
        self.wait_for_input()
    
    def ml_price_prediction(self):
        """ML Price Prediction"""
        if not self.current_data:
            print("❌ No data loaded. Please load cryptocurrency data first.")
            self.wait_for_input()
            return
        
        self.clear_screen()
        self.print_header()
        
        print(f"\n🤖 ML PRICE PREDICTION - {self.current_symbol.upper()}")
        print("-" * 50)
        
        print("🔄 Preparing ML features...")
        
        try:
            prices = self.current_data['prices']
            volumes = self.current_data['volumes']
            
            # Prepare features
            features = self._prepare_ml_features(self.current_data)
            
            if features is None or len(features) < 20:
                print("❌ Insufficient data for ML analysis")
                self.wait_for_input()
                return
            
            print("🔄 Training Linear Regression model...")
            
            # Prepare target
            target = np.diff(prices[-len(features)-1:])[:len(features)]
            
            # Split data
            split_idx = int(len(features) * 0.8)
            X_train, X_test = features[:split_idx], features[split_idx:]
            y_train, y_test = target[:split_idx], target[split_idx:]
            
            # Train model
            model = LinearRegression(learning_rate=0.01, max_iterations=1000)
            model.fit(X_train, y_train)
            
            # Make predictions
            train_pred = model.predict(X_train)
            test_pred = model.predict(X_test)
            
            # Calculate metrics
            train_score = model.score(X_train, y_train)
            test_score = model.score(X_test, y_test)
            
            print("\n🎯 ML PREDICTION RESULTS:")
            print("=" * 40)
            print(f"📊 Training R² Score: {train_score:.3f}")
            print(f"📊 Testing R² Score: {test_score:.3f}")
            
            # Future predictions
            future_pred = model.predict_trend(features[-5:], periods=3)
            current_price = prices[-1]
            
            print(f"\n🔮 PRICE PREDICTIONS:")
            print(f"💰 Current Price: ${current_price:.2f}")
            
            for i, pred in enumerate(future_pred, 1):
                predicted_price = current_price + pred
                change_pct = (pred / current_price) * 100
                direction = "📈" if pred > 0 else "📉"
                print(f"{direction} Period +{i}: ${predicted_price:.2f} ({change_pct:+.2f}%)")
            
            # Feature importance
            importance = model.get_feature_importance()
            if importance is not None:
                print(f"\n🎯 FEATURE IMPORTANCE:")
                feature_names = ['SMA', 'RSI', 'Volume', 'Returns']
                for i, (name, imp) in enumerate(zip(feature_names, importance)):
                    print(f"📊 {name}: {imp:.3f}")
            
        except Exception as e:
            print(f"❌ Error in ML prediction: {e}")

        self.wait_for_input()

    def trading_signal_analysis(self):
        """Trading Signal Analysis"""
        if not self.current_data:
            print("❌ No data loaded. Please load cryptocurrency data first.")
            self.wait_for_input()
            return

        self.clear_screen()
        self.print_header()

        print(f"\n🎯 TRADING SIGNAL ANALYSIS - {self.current_symbol.upper()}")
        print("-" * 50)

        print("🔄 Training trading signal model...")

        try:
            features = self._prepare_ml_features(self.current_data)

            if features is None or len(features) < 20:
                print("❌ Insufficient data for signal analysis")
                self.wait_for_input()
                return

            prices = self.current_data['prices']

            # Create binary target (1 for price increase, 0 for decrease)
            returns = np.diff(prices[-len(features)-1:])[:len(features)]
            target = (returns > 0).astype(int)

            # Split data
            split_idx = int(len(features) * 0.8)
            X_train, X_test = features[:split_idx], features[split_idx:]
            y_train, y_test = target[:split_idx], target[split_idx:]

            # Train Logistic Regression
            signal_model = LogisticRegression(learning_rate=0.1, max_iterations=1000)
            signal_model.fit(X_train, y_train)

            # Generate signals
            signals = signal_model.generate_trading_signals(X_test, buy_threshold=0.7, sell_threshold=0.3)
            probabilities = signal_model.predict_proba(X_test)
            accuracy = signal_model.score(X_test, y_test)

            print("\n🎯 TRADING SIGNAL RESULTS:")
            print("=" * 40)
            print(f"📊 Model Accuracy: {accuracy:.3f}")

            # Count signals
            buy_signals = np.sum(signals == 1)
            sell_signals = np.sum(signals == -1)
            hold_signals = np.sum(signals == 0)

            print(f"📈 Buy Signals: {buy_signals}")
            print(f"📉 Sell Signals: {sell_signals}")
            print(f"⏸️  Hold Signals: {hold_signals}")

            # Current signal
            if len(signals) > 0:
                current_signal = signals[-1]
                current_prob = probabilities[-1]

                signal_text = "🟢 BUY" if current_signal == 1 else "🔴 SELL" if current_signal == -1 else "🟡 HOLD"
                print(f"\n🎯 CURRENT SIGNAL: {signal_text}")
                print(f"📊 Confidence: {current_prob:.3f}")

                # Signal interpretation
                if current_signal == 1:
                    print("💡 Recommendation: Consider buying position")
                elif current_signal == -1:
                    print("💡 Recommendation: Consider selling position")
                else:
                    print("💡 Recommendation: Hold current position")

            # Recent signal history
            print(f"\n📊 RECENT SIGNAL HISTORY:")
            recent_signals = signals[-5:] if len(signals) >= 5 else signals
            for i, sig in enumerate(recent_signals):
                sig_text = "BUY" if sig == 1 else "SELL" if sig == -1 else "HOLD"
                print(f"Period -{len(recent_signals)-i}: {sig_text}")

        except Exception as e:
            print(f"❌ Error in trading signal analysis: {e}")

        self.wait_for_input()

    def market_regime_analysis(self):
        """Market Regime Analysis"""
        if not self.current_data:
            print("❌ No data loaded. Please load cryptocurrency data first.")
            self.wait_for_input()
            return

        self.clear_screen()
        self.print_header()

        print(f"\n🔄 MARKET REGIME ANALYSIS - {self.current_symbol.upper()}")
        print("-" * 50)

        print("🔄 Identifying market regimes...")

        try:
            prices = self.current_data['prices']
            volumes = self.current_data['volumes']

            # Calculate features for regime identification
            returns = np.diff(np.log(prices))
            volatility = np.array([np.std(returns[max(0, i-24):i+1]) for i in range(len(returns))])
            volume_normalized = (volumes[1:] - np.mean(volumes)) / np.std(volumes)

            # Align arrays
            min_len = min(len(returns), len(volatility), len(volume_normalized))

            regime_features = np.column_stack([
                returns[-min_len:],
                volatility[-min_len:],
                volume_normalized[-min_len:]
            ])

            # Normalize features
            regime_features = (regime_features - np.mean(regime_features, axis=0)) / (np.std(regime_features, axis=0) + 1e-8)

            # Apply K-means clustering
            kmeans = KMeans(n_clusters=3, random_state=42)
            labels = kmeans.fit_predict(regime_features)

            # Analyze regimes
            regime_names = ['🟢 Low Volatility', '🟡 Normal Market', '🔴 High Volatility']
            current_regime = labels[-1]

            print("\n🔄 MARKET REGIME RESULTS:")
            print("=" * 40)
            print(f"📊 Identified {kmeans.n_clusters} market regimes")

            for i in range(kmeans.n_clusters):
                regime_points = np.sum(labels == i)
                percentage = (regime_points / len(labels)) * 100
                print(f"📊 Regime {i} ({regime_names[i]}): {regime_points} periods ({percentage:.1f}%)")

            print(f"\n🎯 CURRENT MARKET REGIME:")
            print(f"📊 Regime: {current_regime} ({regime_names[current_regime]})")

            # Regime characteristics
            current_centroid = kmeans.centroids[current_regime]
            print(f"\n📊 REGIME CHARACTERISTICS:")
            print(f"📈 Returns Level: {current_centroid[0]:.3f}")
            print(f"📊 Volatility Level: {current_centroid[1]:.3f}")
            print(f"📊 Volume Level: {current_centroid[2]:.3f}")

            # Regime interpretation
            if current_regime == 0:
                print("\n💡 INTERPRETATION: Stable market conditions with low volatility")
                print("   - Good for trend-following strategies")
                print("   - Lower risk environment")
            elif current_regime == 1:
                print("\n💡 INTERPRETATION: Normal market conditions")
                print("   - Balanced risk-reward environment")
                print("   - Standard trading strategies applicable")
            else:
                print("\n💡 INTERPRETATION: High volatility market conditions")
                print("   - Increased risk and opportunity")
                print("   - Consider risk management strategies")

        except Exception as e:
            print(f"❌ Error in market regime analysis: {e}")

        self.wait_for_input()

    def complete_analysis_report(self):
        """Complete Analysis Report"""
        if not self.current_data:
            print("❌ No data loaded. Please load cryptocurrency data first.")
            self.wait_for_input()
            return

        self.clear_screen()
        self.print_header()

        print(f"\n📋 COMPLETE ANALYSIS REPORT - {self.current_symbol.upper()}")
        print("=" * 60)

        print("🔄 Generating comprehensive analysis...")

        try:
            prices = self.current_data['prices']
            volumes = self.current_data['volumes']

            # Basic info
            print(f"\n📊 MARKET DATA OVERVIEW:")
            print(f"💰 Current Price: ${prices[-1]:.2f}")
            print(f"📊 24h Change: {((prices[-1] - prices[-24]) / prices[-24] * 100):+.2f}%" if len(prices) > 24 else "N/A")
            print(f"📊 Price Range: ${prices.min():.2f} - ${prices.max():.2f}")
            print(f"📊 Data Points: {len(prices)}")

            # Technical Analysis Summary
            print(f"\n📈 TECHNICAL ANALYSIS SUMMARY:")
            rsi = self.indicators.rsi(prices)
            if len(rsi) > 0:
                rsi_status = "Overbought" if rsi[-1] > 70 else "Oversold" if rsi[-1] < 30 else "Neutral"
                print(f"📊 RSI: {rsi[-1]:.1f} ({rsi_status})")

            macd_line, signal_line, histogram = self.indicators.macd(prices)
            if len(histogram) > 0:
                macd_signal = "Bullish" if histogram[-1] > 0 else "Bearish"
                print(f"📊 MACD: {macd_signal}")

            # ML Analysis Summary
            features = self._prepare_ml_features(self.current_data)
            if features is not None and len(features) >= 20:
                print(f"\n🤖 ML ANALYSIS SUMMARY:")

                # Price prediction
                target = np.diff(prices[-len(features)-1:])[:len(features)]
                split_idx = int(len(features) * 0.8)
                X_train, X_test = features[:split_idx], features[split_idx:]
                y_train, y_test = target[:split_idx], target[split_idx:]

                price_model = LinearRegression(learning_rate=0.01, max_iterations=500)
                price_model.fit(X_train, y_train)
                price_score = price_model.score(X_test, y_test)

                print(f"📊 Price Model Accuracy: {price_score:.3f}")

                # Trading signals
                binary_target = (target > 0).astype(int)
                signal_model = LogisticRegression(learning_rate=0.1, max_iterations=500)
                signal_model.fit(X_train, binary_target[:split_idx])
                signal_accuracy = signal_model.score(X_test, binary_target[split_idx:])

                current_signal = signal_model.generate_trading_signals(features[-1:])
                signal_text = "BUY" if current_signal[0] == 1 else "SELL" if current_signal[0] == -1 else "HOLD"

                print(f"📊 Signal Model Accuracy: {signal_accuracy:.3f}")
                print(f"🎯 Current Signal: {signal_text}")

            # Summary & Recommendations
            print(f"\n💡 SUMMARY & RECOMMENDATIONS:")
            print("=" * 40)

            if features is not None and len(features) >= 20:
                if 'signal_text' in locals() and signal_text == "BUY" and rsi[-1] < 70:
                    print("🟢 POSITIVE OUTLOOK: ML signals suggest buying opportunity")
                elif 'signal_text' in locals() and signal_text == "SELL" and rsi[-1] > 30:
                    print("🔴 NEGATIVE OUTLOOK: ML signals suggest selling opportunity")
                else:
                    print("🟡 NEUTRAL OUTLOOK: Mixed signals, consider holding")

            print(f"\n⚠️ DISCLAIMER:")
            print("This analysis is for educational purposes only.")
            print("Always do your own research before making investment decisions.")

        except Exception as e:
            print(f"❌ Error generating report: {e}")

        self.wait_for_input()

    def settings_menu(self):
        """Settings and Configuration"""
        self.clear_screen()
        self.print_header()

        print("\n⚙️ SETTINGS & CONFIGURATION")
        print("-" * 40)
        print("1. 📊 View System Information")
        print("2. 🔧 ML Model Parameters")
        print("3. 📈 Technical Indicator Settings")
        print("4. 🔄 Reset Application")
        print("5. 🔙 Back to Main Menu")

        choice = input("\n👉 Select option (1-5): ").strip()

        if choice == '1':
            self.show_system_info()
        elif choice == '2':
            print("\n🔧 ML Model Parameters:")
            print("- Linear Regression: Learning Rate 0.01, Max Iterations 1000")
            print("- Logistic Regression: Learning Rate 0.1, Max Iterations 1000")
            print("- K-Means: 3 Clusters, Random State 42")
            self.wait_for_input()
        elif choice == '3':
            print("\n📈 Technical Indicator Settings:")
            print("- RSI: 14 periods")
            print("- MACD: 12, 26, 9 periods")
            print("- Bollinger Bands: 20 periods, 2 std dev")
            print("- Moving Averages: SMA(20), SMA(50), EMA(12)")
            self.wait_for_input()
        elif choice == '4':
            self.current_data = None
            self.current_symbol = None
            print("✅ Application reset successfully!")
            self.wait_for_input()

    def show_system_info(self):
        """Show system information"""
        print("\n📊 SYSTEM INFORMATION:")
        print("=" * 30)
        print("🚀 Crypto ML Analysis Application")
        print("💎 Pure NumPy Implementation")
        print("🔧 No ML Frameworks Required")
        print(f"📅 Version: 1.0")
        print(f"🐍 Python: {sys.version.split()[0]}")
        print(f"📊 NumPy: {np.__version__}")

        print(f"\n📈 Available Features:")
        print("✅ Technical Analysis (8+ indicators)")
        print("✅ ML Price Prediction")
        print("✅ Trading Signal Generation")
        print("✅ Market Regime Analysis")
        print("✅ Complete Analysis Reports")

        if self.current_data:
            print(f"\n📊 Current Session:")
            print(f"Asset: {self.current_symbol.upper()}")
            print(f"Data Points: {len(self.current_data['prices'])}")

        self.wait_for_input()

    def help_documentation(self):
        """Help and Documentation"""
        self.clear_screen()
        self.print_header()

        print("\n❓ HELP & DOCUMENTATION")
        print("-" * 40)
        print("1. 📖 Quick Start Guide")
        print("2. 📊 Technical Indicators Guide")
        print("3. 🤖 ML Models Explanation")
        print("4. 🎯 Trading Signals Guide")
        print("5. 🔙 Back to Main Menu")

        choice = input("\n👉 Select option (1-5): ").strip()

        if choice == '1':
            print("\n📖 QUICK START GUIDE:")
            print("=" * 30)
            print("1. 📊 Load cryptocurrency data (Menu option 1)")
            print("2. 📈 Run technical analysis (Menu option 2)")
            print("3. 🤖 Get ML predictions (Menu option 3)")
            print("4. 🎯 Check trading signals (Menu option 4)")
            print("5. 📋 View complete report (Menu option 6)")
            self.wait_for_input()
        elif choice == '2':
            print("\n📊 TECHNICAL INDICATORS:")
            print("- RSI: Relative Strength Index (overbought/oversold)")
            print("- MACD: Moving Average Convergence Divergence")
            print("- Bollinger Bands: Volatility and support/resistance")
            print("- Moving Averages: Trend direction indicators")
            self.wait_for_input()
        elif choice == '3':
            print("\n🤖 ML MODELS:")
            print("- Linear Regression: Price prediction")
            print("- Logistic Regression: Trading signals")
            print("- K-Means: Market regime identification")
            print("- All built with pure NumPy!")
            self.wait_for_input()
        elif choice == '4':
            print("\n🎯 TRADING SIGNALS:")
            print("🟢 BUY: ML predicts price increase")
            print("🔴 SELL: ML predicts price decrease")
            print("🟡 HOLD: Uncertain direction")
            print("\n⚠️ Always use proper risk management!")
            self.wait_for_input()
    
    def _prepare_ml_features(self, data):
        """Prepare features for ML models"""
        prices = data['prices']
        volumes = data['volumes']
        
        if len(prices) < 50:
            return None
        
        try:
            # Calculate returns
            returns = np.diff(np.log(prices))
            
            # Technical indicators as features
            sma_20 = self.indicators.simple_moving_average(prices, 20)
            rsi = self.indicators.rsi(prices)
            volume_sma = self.indicators.simple_moving_average(volumes, 10)
            
            # Align all features to same length
            min_len = min(len(sma_20), len(rsi), len(volume_sma))
            
            if min_len < 10:
                return None
            
            features = np.column_stack([
                sma_20[-min_len:],
                rsi[-min_len:],
                volume_sma[-min_len:],
                returns[-min_len:] if len(returns) >= min_len else np.zeros(min_len)
            ])
            
            # Normalize features
            features_norm, _, _ = self.matrix_ops.normalize(features)
            
            return features_norm
            
        except Exception as e:
            print(f"Error preparing features: {e}")
            return None
    
    def run(self):
        """Main application loop"""
        self.clear_screen()
        
        while True:
            self.clear_screen()
            self.print_header()
            self.print_menu()
            
            choice = self.get_user_choice()
            
            if choice == '1':
                self.load_crypto_data()
            elif choice == '2':
                self.technical_analysis()
            elif choice == '3':
                self.ml_price_prediction()
            elif choice == '4':
                self.trading_signal_analysis()
            elif choice == '5':
                self.market_regime_analysis()
            elif choice == '6':
                self.complete_analysis_report()
            elif choice == '7':
                self.settings_menu()
            elif choice == '8':
                self.help_documentation()
            elif choice == '9':
                self.clear_screen()
                print("👋 Thank you for using Crypto ML Analysis Application!")
                print("💎 Built with pure NumPy - No ML frameworks required!")
                sys.exit(0)
            else:
                print("❌ Invalid choice. Please select 1-9.")
                self.wait_for_input()

def main():
    """Main function"""
    try:
        app = CryptoMLApp()
        app.run()
    except KeyboardInterrupt:
        print("\n\n👋 Application terminated by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please restart the application.")

if __name__ == "__main__":
    main()
