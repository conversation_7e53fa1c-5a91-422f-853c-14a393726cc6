"""
Crypto Market Analyzer - Pure NumPy Implementation
Advanced market analysis for cryptocurrency trading
"""
import numpy as np
from .technical_indicators import TechnicalIndicators

class CryptoMarketAnalyzer:
    """Advanced crypto market analysis tools"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
    
    def analyze_market_structure(self, prices, volumes):
        """
        Analyze overall market structure
        Args:
            prices: Price array
            volumes: Volume array
        Returns:
            Dictionary with market analysis
        """
        returns = np.diff(np.log(prices))
        
        analysis = {
            'trend_strength': self._calculate_trend_strength(prices),
            'volatility_regime': self._classify_volatility_regime(returns),
            'volume_profile': self._analyze_volume_profile(prices, volumes),
            'support_resistance': self._find_support_resistance(prices),
            'market_momentum': self._calculate_momentum(prices),
            'price_efficiency': self._calculate_price_efficiency(returns)
        }
        
        return analysis
    
    def _calculate_trend_strength(self, prices):
        """Calculate trend strength using multiple timeframes"""
        # Short, medium, long term trends
        sma_short = self.indicators.simple_moving_average(prices, 10)
        sma_medium = self.indicators.simple_moving_average(prices, 50)
        sma_long = self.indicators.simple_moving_average(prices, 200)
        
        # Align arrays
        min_len = min(len(sma_short), len(sma_medium), len(sma_long))
        current_price = prices[-min_len:]
        sma_short = sma_short[-min_len:]
        sma_medium = sma_medium[-min_len:]
        sma_long = sma_long[-min_len:]
        
        # Calculate trend scores
        short_trend = np.mean(current_price > sma_short)
        medium_trend = np.mean(sma_short > sma_medium)
        long_trend = np.mean(sma_medium > sma_long)
        
        # Overall trend strength
        trend_strength = (short_trend + medium_trend + long_trend) / 3
        
        return {
            'overall_strength': trend_strength,
            'short_term': short_trend,
            'medium_term': medium_trend,
            'long_term': long_trend,
            'direction': 'bullish' if trend_strength > 0.6 else 'bearish' if trend_strength < 0.4 else 'sideways'
        }
    
    def _classify_volatility_regime(self, returns):
        """Classify current volatility regime"""
        # Calculate rolling volatility
        window = 24  # 24 periods
        volatility = np.zeros(len(returns))
        
        for i in range(window, len(returns)):
            volatility[i] = np.std(returns[i-window:i]) * np.sqrt(24)  # Annualized
        
        # Current volatility vs historical
        current_vol = volatility[-1]
        historical_vol = np.mean(volatility[volatility > 0])
        vol_percentile = np.percentile(volatility[volatility > 0], 75)
        
        if current_vol > vol_percentile:
            regime = 'high_volatility'
        elif current_vol < np.percentile(volatility[volatility > 0], 25):
            regime = 'low_volatility'
        else:
            regime = 'normal_volatility'
        
        return {
            'regime': regime,
            'current_volatility': current_vol,
            'historical_average': historical_vol,
            'volatility_ratio': current_vol / historical_vol if historical_vol > 0 else 1
        }
    
    def _analyze_volume_profile(self, prices, volumes):
        """Analyze volume profile and price-volume relationship"""
        # Price-volume correlation
        price_changes = np.diff(prices)
        volume_changes = np.diff(volumes)
        
        # Align arrays
        min_len = min(len(price_changes), len(volume_changes))
        price_changes = price_changes[-min_len:]
        volume_changes = volume_changes[-min_len:]
        
        correlation = np.corrcoef(np.abs(price_changes), volume_changes)[0, 1]
        
        # Volume trend
        volume_sma = self.indicators.simple_moving_average(volumes, 20)
        volume_trend = 'increasing' if volumes[-1] > volume_sma[-1] else 'decreasing'
        
        # On-Balance Volume
        obv = self.indicators.obv(prices, volumes)
        obv_trend = 'bullish' if obv[-1] > obv[-10] else 'bearish'
        
        return {
            'price_volume_correlation': correlation,
            'volume_trend': volume_trend,
            'obv_trend': obv_trend,
            'average_volume': np.mean(volumes),
            'current_volume_ratio': volumes[-1] / np.mean(volumes)
        }
    
    def _find_support_resistance(self, prices, window=20):
        """Find support and resistance levels"""
        # Local minima and maxima
        highs = []
        lows = []
        
        for i in range(window, len(prices) - window):
            # Local maximum
            if prices[i] == np.max(prices[i-window:i+window+1]):
                highs.append(prices[i])
            
            # Local minimum
            if prices[i] == np.min(prices[i-window:i+window+1]):
                lows.append(prices[i])
        
        # Cluster similar levels
        resistance_levels = self._cluster_levels(highs) if highs else []
        support_levels = self._cluster_levels(lows) if lows else []
        
        current_price = prices[-1]
        
        return {
            'resistance_levels': resistance_levels,
            'support_levels': support_levels,
            'nearest_resistance': min(resistance_levels, key=lambda x: abs(x - current_price)) if resistance_levels else None,
            'nearest_support': min(support_levels, key=lambda x: abs(x - current_price)) if support_levels else None
        }
    
    def _cluster_levels(self, levels, threshold=0.02):
        """Cluster similar price levels"""
        if not levels:
            return []
        
        levels = np.array(levels)
        clustered = []
        
        for level in levels:
            # Check if this level is close to any existing cluster
            close_to_existing = False
            for i, cluster_level in enumerate(clustered):
                if abs(level - cluster_level) / cluster_level < threshold:
                    # Update cluster level (weighted average)
                    clustered[i] = (clustered[i] + level) / 2
                    close_to_existing = True
                    break
            
            if not close_to_existing:
                clustered.append(level)
        
        return sorted(clustered)
    
    def _calculate_momentum(self, prices):
        """Calculate various momentum indicators"""
        rsi = self.indicators.rsi(prices)
        macd_line, signal_line, histogram = self.indicators.macd(prices)
        
        # Rate of Change
        roc_period = 10
        roc = (prices[roc_period:] - prices[:-roc_period]) / prices[:-roc_period] * 100
        
        return {
            'rsi': rsi[-1] if len(rsi) > 0 else 50,
            'macd_signal': 'bullish' if histogram[-1] > 0 else 'bearish' if len(histogram) > 0 else 'neutral',
            'rate_of_change': roc[-1] if len(roc) > 0 else 0,
            'momentum_score': self._calculate_momentum_score(rsi, histogram, roc)
        }
    
    def _calculate_momentum_score(self, rsi, histogram, roc):
        """Calculate composite momentum score"""
        score = 0
        
        # RSI component
        if len(rsi) > 0:
            if rsi[-1] > 70:
                score += 1  # Overbought but strong
            elif rsi[-1] > 50:
                score += 0.5
            elif rsi[-1] < 30:
                score -= 1  # Oversold
            else:
                score -= 0.5
        
        # MACD component
        if len(histogram) > 0:
            if histogram[-1] > 0:
                score += 0.5
            else:
                score -= 0.5
        
        # ROC component
        if len(roc) > 0:
            if roc[-1] > 5:
                score += 0.5
            elif roc[-1] < -5:
                score -= 0.5
        
        return score
    
    def _calculate_price_efficiency(self, returns):
        """Calculate market efficiency metrics"""
        # Hurst exponent approximation
        def hurst_exponent(ts):
            lags = range(2, min(100, len(ts)//4))
            tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]
            poly = np.polyfit(np.log(lags), np.log(tau), 1)
            return poly[0] * 2.0
        
        hurst = hurst_exponent(returns) if len(returns) > 10 else 0.5
        
        # Autocorrelation
        autocorr = np.corrcoef(returns[1:], returns[:-1])[0, 1] if len(returns) > 1 else 0
        
        return {
            'hurst_exponent': hurst,
            'autocorrelation': autocorr,
            'efficiency_score': abs(0.5 - hurst) + abs(autocorr),  # Lower is more efficient
            'market_type': 'trending' if hurst > 0.55 else 'mean_reverting' if hurst < 0.45 else 'random_walk'
        }
