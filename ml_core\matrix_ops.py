"""
Matrix Operations - Pure NumPy Implementation
Core mathematical operations for ML algorithms
"""
import numpy as np

class MatrixOps:
    """Basic matrix operations for ML algorithms"""
    
    @staticmethod
    def normalize(X, method='standard'):
        """
        Normalize data
        Args:
            X: Input data (n_samples, n_features)
            method: 'standard', 'minmax', or 'robust'
        """
        if method == 'standard':
            mean = np.mean(X, axis=0)
            std = np.std(X, axis=0)
            return (X - mean) / (std + 1e-8), mean, std
        
        elif method == 'minmax':
            min_val = np.min(X, axis=0)
            max_val = np.max(X, axis=0)
            return (X - min_val) / (max_val - min_val + 1e-8), min_val, max_val
        
        elif method == 'robust':
            median = np.median(X, axis=0)
            mad = np.median(np.abs(X - median), axis=0)
            return (X - median) / (mad + 1e-8), median, mad
    
    @staticmethod
    def add_bias(X):
        """Add bias column to feature matrix"""
        return np.column_stack([np.ones(X.shape[0]), X])
    
    @staticmethod
    def train_test_split(X, y, test_size=0.2, random_state=None):
        """Split data into train and test sets"""
        if random_state:
            np.random.seed(random_state)
        
        n_samples = X.shape[0]
        n_test = int(n_samples * test_size)
        
        indices = np.random.permutation(n_samples)
        test_indices = indices[:n_test]
        train_indices = indices[n_test:]
        
        return X[train_indices], X[test_indices], y[train_indices], y[test_indices]
    
    @staticmethod
    def moving_average(data, window):
        """Calculate moving average"""
        return np.convolve(data, np.ones(window)/window, mode='valid')
    
    @staticmethod
    def exponential_moving_average(data, alpha):
        """Calculate exponential moving average"""
        ema = np.zeros_like(data)
        ema[0] = data[0]
        
        for i in range(1, len(data)):
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    @staticmethod
    def correlation_matrix(X):
        """Calculate correlation matrix"""
        X_centered = X - np.mean(X, axis=0)
        cov_matrix = np.dot(X_centered.T, X_centered) / (X.shape[0] - 1)
        
        # Calculate standard deviations
        std_devs = np.sqrt(np.diag(cov_matrix))
        
        # Calculate correlation matrix
        corr_matrix = cov_matrix / np.outer(std_devs, std_devs)
        
        return corr_matrix
    
    @staticmethod
    def rolling_window(data, window_size):
        """Create rolling windows from time series data"""
        shape = (data.shape[0] - window_size + 1, window_size)
        strides = (data.strides[0], data.strides[0])
        return np.lib.stride_tricks.as_strided(data, shape=shape, strides=strides)
