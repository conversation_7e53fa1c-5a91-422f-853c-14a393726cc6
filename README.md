# 🏛️ Crypto Bloomberg Terminal

**Professional Cryptocurrency Analysis Terminal** - Sistem analisis cryptocurrency dengan interface bergaya Bloomberg Terminal yang dibangun dari nol menggunakan **pure NumPy**.

## 🎯 Fitur Bloomberg Terminal

### 🖥️ Professional Terminal Interface
- **Bloomberg-style Layout** - Multi-panel professional interface
- **Real-time Market Data** - Live cryptocurrency prices dan updates
- **Professional Charts** - Advanced technical analysis charts
- **Market Watchlist** - Real-time cryptocurrency monitoring
- **News & Analytics** - Integrated market news dan analysis



### 🔧 Core ML Algorithms (Pure NumPy)
- **Linear Regression** - Prediksi harga dan trend analysis
- **Logistic Regression** - Klasifikasi buy/sell signals
- **K-Means Clustering** - Identifikasi market regimes
- **Matrix Operations** - Operasi matematika dasar untuk ML

### 📊 Technical Analysis
- **Moving Averages** (SMA, EMA)
- **RSI** (Relative Strength Index)
- **MACD** (Moving Average Convergence Divergence)
- **Bollinger Bands**
- **Stochastic Oscillator**
- **Williams %R**
- **ATR** (Average True Range)
- **OBV** (On-Balance Volume)

### 🎯 Crypto-Specific Analysis
- **Price Prediction** menggunakan technical indicators
- **Trading Signal Generation** (Buy/Sell/Hold)
- **Market Regime Identification** (Bull/Bear/Sideways)
- **Volatility Analysis** dan risk metrics
- **Support/Resistance Level Detection**

### 📈 Data Sources
- **CoinGecko API** - Historical price data
- **Binance API** - Real-time OHLCV data
- **Synthetic Data Generator** - Untuk testing dan demo

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Opsi 1: Gunakan batch file (Recommended)
.\install_requirements.bat

# Opsi 2: Manual install
pip install numpy requests matplotlib
```

### 2. Launch Bloomberg Terminal
```bash
# 🏛️ BLOOMBERG TERMINAL (RECOMMENDED)
.\run_gui.bat           # Launch professional terminal interface
py crypto_ml_gui.py     # Atau jalankan langsung

# 🐍 Jika Python sudah di PATH
python crypto_ml_gui.py # Bloomberg Terminal

# 📁 Gunakan path lengkap Python (jika diperlukan)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_ml_gui.py
```

## 📁 Struktur Project

```
ML/
├── crypto_ml_gui.py           # 🏛️ BLOOMBERG TERMINAL APPLICATION
├── ml_core/                   # Core ML algorithms (Pure NumPy)
│   ├── matrix_ops.py          # Matrix operations
│   ├── linear_regression.py   # Linear regression
│   ├── logistic_regression.py # Logistic regression
│   └── kmeans.py              # K-means clustering
├── crypto_analysis/           # Crypto-specific analysis
│   ├── data_fetcher.py        # Data fetching from APIs
│   ├── technical_indicators.py # Technical analysis
│   └── market_analyzer.py     # Market structure analysis
├── run_gui.bat               # 🏛️ Launch Bloomberg Terminal
├── install_requirements.bat  # Install dependencies
└── requirements.txt          # Dependencies
```

## 🔍 Bloomberg Terminal Features

### 🏛️ Professional Terminal Interface
- **Multi-Panel Layout**: Market data, charts, analytics, dan news dalam satu layar
- **Real-time Updates**: Live cryptocurrency prices dan market status
- **Professional Charts**: Advanced technical analysis dengan Bloomberg styling
- **Market Watchlist**: Real-time monitoring multiple cryptocurrencies
- **Integrated Analytics**: Technical indicators, ML predictions, trading signals
- **Market News**: Real-time market updates dan analysis

### 🤖 ML Algorithms (Pure NumPy)
- **Linear Regression**: Price prediction and trend analysis
- **Logistic Regression**: Buy/sell signal classification
- **K-Means Clustering**: Market regime identification
- **Matrix Operations**: Optimized mathematical computations

### 📊 Technical Indicators
- Moving Averages (SMA, EMA), RSI, MACD, Bollinger Bands
- Stochastic Oscillator, Williams %R, ATR, OBV
- Volume analysis and volatility metrics

## 🎯 Supported Cryptocurrencies

Bitcoin (BTC), Ethereum (ETH), Cardano (ADA), Solana (SOL), dan cryptocurrency lainnya via API

## 🚀 Getting Started

1. **Install Dependencies**: Run `.\install_requirements.bat`
2. **Launch Bloomberg Terminal**: Run `.\run_gui.bat`
3. **Select Cryptocurrency**: Click on any crypto in the watchlist
4. **Analyze Markets**: Use professional charts dan technical analysis
5. **Monitor Real-time**: Watch live prices dan market updates
6. **Generate Signals**: Use ML predictions untuk trading insights

## 🎯 Fully Functional Features

### ✅ **Real-time Bloomberg Interface**
- **Live Clock**: Real-time UTC clock updates setiap detik
- **Market Status**: 24/7 crypto market monitoring
- **Live Prices**: Real-time cryptocurrency prices dengan market simulation
- **Data Sources**: Multiple API fallback (CoinCap, Binance, Live Simulation)
- **Price Updates**: Automatic refresh setiap 30 detik
- **Professional Layout**: Multi-panel Bloomberg-style interface

### ✅ **Advanced Technical Analysis**
- **Moving Averages**: SMA(20), SMA(50), EMA(12)
- **Momentum Indicators**: RSI, MACD, Histogram
- **Volatility Analysis**: Bollinger Bands
- **Volume Analysis**: OBV, Volume ratios
- **Professional ASCII Tables**: Bloomberg-style data display

### ✅ **Machine Learning Predictions**
- **Price Prediction**: Linear regression dengan R² scoring
- **Feature Engineering**: Technical indicators sebagai features
- **Future Forecasting**: 5-period price predictions
- **Model Performance**: Training/testing accuracy metrics
- **Professional Display**: Bloomberg-style prediction tables

### ✅ **Trading Signal Generation**
- **Multi-indicator Signals**: RSI, MACD, Moving Averages
- **Signal Scoring**: Weighted scoring system
- **Risk Management**: Support/resistance levels
- **Position Sizing**: Risk-based position recommendations
- **Professional Format**: Bloomberg-style signal tables

### ✅ **Market Regime Analysis**
- **K-means Clustering**: 3-regime classification (Bull/Bear/Sideways)
- **Regime Characteristics**: Volatility, momentum, volume analysis
- **Stability Analysis**: Regime change frequency
- **Trading Strategy**: Regime-based recommendations
- **Professional Display**: Bloomberg-style regime tables

### ✅ **Professional Charts**
- **Multi-timeframe**: 1H, 4H, 1D, 1W, 1M
- **Technical Overlays**: Moving averages, Bollinger Bands
- **Sub-charts**: Volume dan RSI analysis
- **Bloomberg Styling**: Dark theme dengan professional colors
- **Real-time Updates**: Automatic chart refreshing

## 🎮 How to Use Bloomberg Terminal

### **1. Launch Terminal**
```bash
.\run_gui.bat
```

### **2. Interface Overview**
- **Left Panel**: Market watchlist dengan live prices
- **Center Panel**: Professional charts dengan technical analysis
- **Right Panel**: Analytics tabs (Technical, ML, Signals, News)
- **Header**: Real-time clock dan market status
- **Footer**: System status updates

### **3. Select Cryptocurrency**
Click any cryptocurrency in the left watchlist to load data

### **4. Run Analysis**
- **Technical Analysis**: Click "TECHNICAL" button
- **ML Predictions**: Click "ML PREDICT" button
- **Trading Signals**: Use "Generate Signals" function
- **Market Regimes**: Run regime analysis

### **5. Change Timeframes**
Use timeframe buttons: 1H, 4H, 1D, 1W, 1M

## 🎯 Supported Cryptocurrencies

Bitcoin (BTC), Ethereum (ETH), Cardano (ADA), Solana (SOL), dan cryptocurrency lainnya via API

## ⚡ Performance & Benefits

- **Pure NumPy Implementation** - No ML framework dependencies
- **Real-time Updates** - Live market data dan clock
- **Professional Interface** - Bloomberg Terminal styling
- **Educational Focus** - Learn ML algorithms from scratch
- **Fully Functional** - All features working perfectly

## 🛡️ Important Disclaimer

⚠️ **Educational Purpose Only**: This system is for learning and research. Not financial advice.

---

**🏛️ Professional Bloomberg Terminal - Built with Pure NumPy!**
