# Crypto ML Analysis System 🚀

Sistem Machine Learning untuk analisis cryptocurrency yang dibangun dari nol menggunakan **pure NumPy** tanpa framework ML seperti Scikit-learn, TensorFlow, atau PyTorch.

## 🎯 Fitur Utama

### 🖥️ GUI Desktop Application
- **Modern Tkinter Interface** - Desktop GUI dengan dark theme
- **Interactive Charts** - Visualisasi data dengan matplotlib
- **5-Tab Interface** - Data, Analysis, ML, Signals, Charts
- **Real-time Processing** - Background threading untuk performance



### 🔧 Core ML Algorithms (Pure NumPy)
- **Linear Regression** - Prediksi harga dan trend analysis
- **Logistic Regression** - Klasifikasi buy/sell signals
- **K-Means Clustering** - Identifikasi market regimes
- **Matrix Operations** - Operasi matematika dasar untuk ML

### 📊 Technical Analysis
- **Moving Averages** (SMA, EMA)
- **RSI** (Relative Strength Index)
- **MACD** (Moving Average Convergence Divergence)
- **Bollinger Bands**
- **Stochastic Oscillator**
- **Williams %R**
- **ATR** (Average True Range)
- **OBV** (On-Balance Volume)

### 🎯 Crypto-Specific Analysis
- **Price Prediction** menggunakan technical indicators
- **Trading Signal Generation** (Buy/Sell/Hold)
- **Market Regime Identification** (Bull/Bear/Sideways)
- **Volatility Analysis** dan risk metrics
- **Support/Resistance Level Detection**

### 📈 Data Sources
- **CoinGecko API** - Historical price data
- **Binance API** - Real-time OHLCV data
- **Synthetic Data Generator** - Untuk testing dan demo

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Opsi 1: Gunakan batch file (Recommended)
.\install_requirements.bat

# Opsi 2: Manual install
pip install numpy requests matplotlib
```

### 2. Run Application
```bash
# 🖥️ GUI DESKTOP APPLICATION (RECOMMENDED)
.\run_gui.bat           # Modern GUI dengan charts dan visualisasi
py crypto_ml_gui.py     # Atau jalankan langsung

# 🐍 Jika Python sudah di PATH
python crypto_ml_gui.py # GUI Desktop Application

# 📁 Gunakan path lengkap Python (jika diperlukan)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_ml_gui.py
```

## 📁 Struktur Project

```
ML/
├── crypto_ml_gui.py           # 🖥️ GUI DESKTOP APPLICATION
├── ml_core/                   # Core ML algorithms (Pure NumPy)
│   ├── matrix_ops.py          # Matrix operations
│   ├── linear_regression.py   # Linear regression
│   ├── logistic_regression.py # Logistic regression
│   └── kmeans.py              # K-means clustering
├── crypto_analysis/           # Crypto-specific analysis
│   ├── data_fetcher.py        # Data fetching from APIs
│   ├── technical_indicators.py # Technical analysis
│   └── market_analyzer.py     # Market structure analysis
├── run_gui.bat               # 🖥️ Jalankan GUI desktop app
├── install_requirements.bat  # Install dependencies
└── requirements.txt          # Dependencies
```

## 🔍 Features Overview

### 🖥️ GUI Application Features
- **5-Tab Interface**: Data Loading, Technical Analysis, ML Prediction, Trading Signals, Charts
- **Real-time Analysis**: Background processing with progress indicators
- **Interactive Charts**: Matplotlib integration for data visualization
- **Built-in Testing**: System test functionality to verify all components
- **Dark Theme**: Modern UI with professional appearance

### 🤖 ML Algorithms (Pure NumPy)
- **Linear Regression**: Price prediction and trend analysis
- **Logistic Regression**: Buy/sell signal classification
- **K-Means Clustering**: Market regime identification
- **Matrix Operations**: Optimized mathematical computations

### 📊 Technical Indicators
- Moving Averages (SMA, EMA), RSI, MACD, Bollinger Bands
- Stochastic Oscillator, Williams %R, ATR, OBV
- Volume analysis and volatility metrics

## 🎯 Supported Cryptocurrencies

Bitcoin (BTC), Ethereum (ETH), Cardano (ADA), Solana (SOL), dan cryptocurrency lainnya via API

## 🚀 Getting Started

1. **Install Dependencies**: Run `.\install_requirements.bat`
2. **Launch Application**: Run `.\run_gui.bat`
3. **Load Data**: Select cryptocurrency and time period
4. **Run Analysis**: Use the built-in test system or analyze real data
5. **View Results**: Explore technical analysis, ML predictions, and trading signals

## ⚡ Performance & Benefits

- **Pure NumPy Implementation** - No ML framework dependencies
- **Lightweight & Fast** - Optimized for performance
- **Educational Focus** - Learn ML algorithms from scratch
- **Extensible Design** - Easy to add new indicators and models

## 🛡️ Important Disclaimer

⚠️ **Educational Purpose Only**: This system is for learning and research. Not financial advice.

---

**🚀 Built with Pure NumPy - No ML Frameworks Required!**
