# 🏛️ Crypto Bloomberg Terminal

**Professional Cryptocurrency Analysis Terminal** - Sistem analisis cryptocurrency dengan interface bergaya Bloomberg Terminal yang dibangun dari nol menggunakan **pure NumPy**.

## 🎯 Fitur Bloomberg Terminal

### 🖥️ Professional Terminal Interface
- **Bloomberg-style Layout** - Multi-panel professional interface
- **Real-time Market Data** - Live cryptocurrency prices dan updates
- **Professional Charts** - Advanced technical analysis charts
- **Market Watchlist** - Real-time cryptocurrency monitoring
- **News & Analytics** - Integrated market news dan analysis



### 🔧 Core ML Algorithms (Pure NumPy)
- **Linear Regression** - Prediksi harga dan trend analysis
- **Logistic Regression** - Klasifikasi buy/sell signals
- **K-Means Clustering** - Identifikasi market regimes
- **Matrix Operations** - Operasi matematika dasar untuk ML

### 📊 Technical Analysis
- **Moving Averages** (SMA, EMA)
- **RSI** (Relative Strength Index)
- **MACD** (Moving Average Convergence Divergence)
- **Bollinger Bands**
- **Stochastic Oscillator**
- **Williams %R**
- **ATR** (Average True Range)
- **OBV** (On-Balance Volume)

### 🎯 Crypto-Specific Analysis
- **Price Prediction** menggunakan technical indicators
- **Trading Signal Generation** (Buy/Sell/Hold)
- **Market Regime Identification** (Bull/Bear/Sideways)
- **Volatility Analysis** dan risk metrics
- **Support/Resistance Level Detection**

### 📈 Data Sources
- **CoinGecko API** - Historical price data
- **Binance API** - Real-time OHLCV data
- **Synthetic Data Generator** - Untuk testing dan demo

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Opsi 1: Gunakan batch file (Recommended)
.\install_requirements.bat

# Opsi 2: Manual install
pip install numpy requests matplotlib
```

### 2. Launch Bloomberg Terminal
```bash
# 🏛️ BLOOMBERG TERMINAL (RECOMMENDED)
.\run_gui.bat           # Launch professional terminal interface
py crypto_ml_gui.py     # Atau jalankan langsung

# 🐍 Jika Python sudah di PATH
python crypto_ml_gui.py # Bloomberg Terminal

# 📁 Gunakan path lengkap Python (jika diperlukan)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_ml_gui.py
```

## 📁 Struktur Project

```
ML/
├── crypto_ml_gui.py           # 🏛️ BLOOMBERG TERMINAL APPLICATION
├── ml_core/                   # Core ML algorithms (Pure NumPy)
│   ├── matrix_ops.py          # Matrix operations
│   ├── linear_regression.py   # Linear regression
│   ├── logistic_regression.py # Logistic regression
│   └── kmeans.py              # K-means clustering
├── crypto_analysis/           # Crypto-specific analysis
│   ├── data_fetcher.py        # Data fetching from APIs
│   ├── technical_indicators.py # Technical analysis
│   └── market_analyzer.py     # Market structure analysis
├── run_gui.bat               # 🏛️ Launch Bloomberg Terminal
├── install_requirements.bat  # Install dependencies
└── requirements.txt          # Dependencies
```

## 🔍 Bloomberg Terminal Features

### 🏛️ Professional Terminal Interface
- **Multi-Panel Layout**: Market data, charts, analytics, dan news dalam satu layar
- **Real-time Updates**: Live cryptocurrency prices dan market status
- **Professional Charts**: Advanced technical analysis dengan Bloomberg styling
- **Market Watchlist**: Real-time monitoring multiple cryptocurrencies
- **Integrated Analytics**: Technical indicators, ML predictions, trading signals
- **Market News**: Real-time market updates dan analysis

### 🤖 ML Algorithms (Pure NumPy)
- **Linear Regression**: Price prediction and trend analysis
- **Logistic Regression**: Buy/sell signal classification
- **K-Means Clustering**: Market regime identification
- **Matrix Operations**: Optimized mathematical computations

### 📊 Technical Indicators
- Moving Averages (SMA, EMA), RSI, MACD, Bollinger Bands
- Stochastic Oscillator, Williams %R, ATR, OBV
- Volume analysis and volatility metrics

## 🎯 Supported Cryptocurrencies

Bitcoin (BTC), Ethereum (ETH), Cardano (ADA), Solana (SOL), dan cryptocurrency lainnya via API

## 🚀 Getting Started

1. **Install Dependencies**: Run `.\install_requirements.bat`
2. **Launch Bloomberg Terminal**: Run `.\run_gui.bat`
3. **Select Cryptocurrency**: Click on any crypto in the watchlist
4. **Analyze Markets**: Use professional charts dan technical analysis
5. **Monitor Real-time**: Watch live prices dan market updates
6. **Generate Signals**: Use ML predictions untuk trading insights

## ⚡ Performance & Benefits

- **Pure NumPy Implementation** - No ML framework dependencies
- **Lightweight & Fast** - Optimized for performance
- **Educational Focus** - Learn ML algorithms from scratch
- **Extensible Design** - Easy to add new indicators and models

## 🛡️ Important Disclaimer

⚠️ **Educational Purpose Only**: This system is for learning and research. Not financial advice.

---

**🚀 Built with Pure NumPy - No ML Frameworks Required!**
