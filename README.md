# Crypto ML Analysis System 🚀

Sistem Machine Learning untuk analisis cryptocurrency yang dibangun dari nol menggunakan **pure NumPy** tanpa framework ML seperti Scikit-learn, TensorFlow, atau PyTorch.

## 🎯 Fitur Utama

### 🖥️ GUI Desktop Application
- **Modern Tkinter Interface** - Desktop GUI dengan dark theme
- **Interactive Charts** - Visualisasi data dengan matplotlib
- **5-Tab Interface** - Data, Analysis, ML, Signals, Charts
- **Real-time Processing** - Background threading untuk performance

### 📱 Console Interactive Application
- **Menu-Driven Interface** - Aplikasi interaktif dengan menu
- **Real-time Analysis** - Analisis cryptocurrency real-time
- **User-Friendly** - Interface yang mudah digunakan
- **Comprehensive Reports** - Laporan analisis lengkap

### 🔧 Core ML Algorithms (Pure NumPy)
- **Linear Regression** - Prediksi harga dan trend analysis
- **Logistic Regression** - Klasifikasi buy/sell signals
- **K-Means Clustering** - Identifikasi market regimes
- **Matrix Operations** - Operasi matematika dasar untuk ML

### 📊 Technical Analysis
- **Moving Averages** (SMA, EMA)
- **RSI** (Relative Strength Index)
- **MACD** (Moving Average Convergence Divergence)
- **Bollinger Bands**
- **Stochastic Oscillator**
- **Williams %R**
- **ATR** (Average True Range)
- **OBV** (On-Balance Volume)

### 🎯 Crypto-Specific Analysis
- **Price Prediction** menggunakan technical indicators
- **Trading Signal Generation** (Buy/Sell/Hold)
- **Market Regime Identification** (Bull/Bear/Sideways)
- **Volatility Analysis** dan risk metrics
- **Support/Resistance Level Detection**

### 📈 Data Sources
- **CoinGecko API** - Historical price data
- **Binance API** - Real-time OHLCV data
- **Synthetic Data Generator** - Untuk testing dan demo

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Opsi 1: Gunakan batch file (Recommended)
.\install_requirements.bat

# Opsi 2: Manual install
pip install numpy requests matplotlib
```

### 2. Run Analysis (Pilih salah satu)
```bash
# 🖥️ GUI DESKTOP APPLICATION (RECOMMENDED)
.\run_gui.bat           # Modern GUI dengan charts dan visualisasi
py crypto_ml_gui.py     # Atau jalankan langsung

# 📱 CONSOLE INTERACTIVE APPLICATION
.\run_app.bat           # Aplikasi dengan menu interaktif
py crypto_ml_app.py     # Atau jalankan langsung

# 📊 Demo & Testing
.\run_demo.bat          # Demo sistem
.\run_main.bat          # Analisis lengkap
.\run_test.bat          # Test sistem

# 🐍 Jika Python sudah di PATH
python crypto_ml_gui.py # GUI Desktop Application
python crypto_ml_app.py # Console Interactive Application
python demo.py          # Demo
python main.py          # Analisis lengkap

# 📁 Gunakan path lengkap Python (jika diperlukan)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_ml_gui.py
```

### 3. Troubleshooting Python Path
Jika muncul error "Python was not found", lihat **SETUP_GUIDE.md** untuk solusi lengkap.

## 📁 Struktur Project

```
ML/
├── crypto_ml_gui.py           # 🖥️ GUI DESKTOP APPLICATION
├── crypto_ml_app.py           # 📱 CONSOLE INTERACTIVE APPLICATION
├── ml_core/                   # Core ML algorithms (Pure NumPy)
│   ├── matrix_ops.py          # Matrix operations
│   ├── linear_regression.py   # Linear regression
│   ├── logistic_regression.py # Logistic regression
│   └── kmeans.py              # K-means clustering
├── crypto_analysis/           # Crypto-specific analysis
│   ├── data_fetcher.py        # Data fetching from APIs
│   ├── technical_indicators.py # Technical analysis
│   └── market_analyzer.py     # Market structure analysis
├── examples/                  # Usage examples
├── run_gui.bat               # 🖥️ Jalankan GUI desktop app
├── run_app.bat               # 📱 Jalankan console interactive app
├── run_demo.bat              # Demo sistem
├── run_main.bat              # Analisis lengkap
├── main.py                   # Main entry point
├── demo.py                   # Demo sederhana
└── requirements.txt          # Dependencies
```

## 🔍 Contoh Penggunaan

### Basic Price Analysis
```python
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators

# Fetch Bitcoin data
fetcher = CryptoDataFetcher()
data = fetcher.fetch_price_history('bitcoin', days=30)

# Calculate technical indicators
indicators = TechnicalIndicators()
rsi = indicators.rsi(data['prices'])
sma_20 = indicators.simple_moving_average(data['prices'], 20)

print(f"Current RSI: {rsi[-1]:.1f}")
print(f"Current SMA(20): {sma_20[-1]:.2f}")
```

### ML Price Prediction
```python
from ml_core.linear_regression import LinearRegression

# Prepare features and train model
model = LinearRegression(learning_rate=0.01)
model.fit(X_train, y_train)

# Make predictions
predictions = model.predict(X_test)
score = model.score(X_test, y_test)
print(f"Model R² Score: {score:.3f}")
```

### Trading Signal Generation
```python
from ml_core.logistic_regression import LogisticRegression

# Train signal classifier
signal_model = LogisticRegression()
signal_model.fit(features, buy_sell_labels)

# Generate trading signals
signals = signal_model.generate_trading_signals(new_data)
# signals: 1=Buy, -1=Sell, 0=Hold
```

### Market Regime Analysis
```python
from ml_core.kmeans import KMeans

# Identify market regimes
kmeans = KMeans(n_clusters=3)
regimes = kmeans.fit_predict(market_features)

# Current market regime
current_regime = regimes[-1]
print(f"Current Market Regime: {current_regime}")
```

## 🎯 Supported Cryptocurrencies

- Bitcoin (BTC)
- Ethereum (ETH)
- Cardano (ADA)
- Solana (SOL)
- Dan cryptocurrency lainnya yang tersedia di CoinGecko/Binance

## 📊 Output Analysis

Sistem menghasilkan analisis komprehensif meliputi:

1. **Trend Analysis** - Arah dan kekuatan trend
2. **Volatility Regime** - Klasifikasi volatilitas pasar
3. **Technical Indicators** - RSI, MACD, Bollinger Bands, dll
4. **ML Predictions** - Prediksi harga dan akurasi model
5. **Trading Signals** - Sinyal buy/sell dengan probabilitas
6. **Market Regimes** - Identifikasi kondisi pasar saat ini

## 🔧 Customization

### Menambah Indikator Baru
```python
# Di technical_indicators.py
@staticmethod
def custom_indicator(prices, param1, param2):
    # Implementasi indikator custom
    return result
```

### Menambah Model ML Baru
```python
# Buat file baru di ml_core/
class CustomMLModel:
    def __init__(self):
        pass
    
    def fit(self, X, y):
        # Training logic
        pass
    
    def predict(self, X):
        # Prediction logic
        pass
```

## ⚡ Performance

- **Pure NumPy** - Performa tinggi tanpa overhead framework
- **Memory Efficient** - Optimized untuk dataset besar
- **Fast Computation** - Vectorized operations
- **Scalable** - Mudah di-extend untuk crypto lainnya

## 🛡️ Risk Disclaimer

⚠️ **PERINGATAN**: Sistem ini dibuat untuk tujuan edukasi dan penelitian. 
- Tidak memberikan saran investasi
- Cryptocurrency trading memiliki risiko tinggi
- Selalu lakukan riset sendiri sebelum trading
- Gunakan hanya dana yang siap hilang

## 🤝 Contributing

Kontribusi sangat diterima! Silakan:
1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Create Pull Request

## 📄 License

MIT License - Bebas digunakan untuk tujuan edukasi dan komersial.

---

**Built with ❤️ using Pure NumPy - No ML Frameworks Required!**
