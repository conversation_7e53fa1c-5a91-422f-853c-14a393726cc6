@echo off
echo 🚀 Running Crypto ML Demo...
echo ================================

REM Try multiple Python paths
set PYTHON_PATH1=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
set PYTHON_PATH2=C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe
set PYTHON_PATH3=py

REM Check which Python exists
if exist "%PYTHON_PATH1%" (
    set PYTHON_PATH=%PYTHON_PATH1%
    echo 🐍 Using Python at: %PYTHON_PATH1%
) else if exist "%PYTHON_PATH2%" (
    set PYTHON_PATH=%PYTHON_PATH2%
    echo 🐍 Using Python at: %PYTHON_PATH2%
) else (
    set PYTHON_PATH=%PYTHON_PATH3%
    echo 🐍 Using Python launcher: py
)

REM Run the demo
echo 📊 Starting demo...
echo.
"%PYTHON_PATH%" demo.py

echo.
echo ✅ Demo completed!
pause
