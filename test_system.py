"""
Simple test for the Crypto ML System
"""
import numpy as np
from crypto_analysis.data_fetcher import CryptoDataFetcher
from crypto_analysis.technical_indicators import TechnicalIndicators
from ml_core.linear_regression import LinearRegression

def test_basic_functionality():
    print("🧪 Testing Crypto ML System...")
    
    # Test 1: Data Fetcher
    print("1. Testing Data Fetcher...")
    fetcher = CryptoDataFetcher()
    data = fetcher._generate_synthetic_data('bitcoin', 30)  # Use synthetic data
    print(f"   ✅ Generated {len(data['prices'])} price points")
    
    # Test 2: Technical Indicators
    print("2. Testing Technical Indicators...")
    indicators = TechnicalIndicators()
    
    prices = data['prices']
    sma = indicators.simple_moving_average(prices, 10)
    rsi = indicators.rsi(prices)
    
    print(f"   ✅ SMA(10): {sma[-1]:.2f}")
    print(f"   ✅ RSI: {rsi[-1]:.1f}")
    
    # Test 3: Linear Regression
    print("3. Testing Linear Regression...")
    
    # Simple features
    X = np.column_stack([
        sma[-50:],
        rsi[-50:]
    ])
    
    # Target: price changes
    y = np.diff(prices[-51:])
    
    # Normalize
    X = (X - np.mean(X, axis=0)) / np.std(X, axis=0)
    
    # Train model
    model = LinearRegression(learning_rate=0.01, max_iterations=100)
    model.fit(X, y)
    
    # Test prediction
    pred = model.predict(X[-5:])
    print(f"   ✅ Model trained successfully")
    print(f"   ✅ Sample predictions: {pred[:3]}")
    
    print("\n🎉 All tests passed! System is working correctly.")
    return True

if __name__ == "__main__":
    test_basic_functionality()
