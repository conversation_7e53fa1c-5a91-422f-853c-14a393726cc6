# 🚀 QUICK START - Crypto ML System

## ⚡ Cara Tercepat Menjalankan Sistem

### 1. Buka Command Prompt atau PowerShell
```bash
# Tekan Win + R, ketik: cmd atau powershell
# Navigate ke folder ML
cd D:\xxamp\htdocs\ML
```

### 2. Install Dependencies (Sekali saja)
```bash
py -m pip install numpy requests matplotlib
```

### 3. Jalankan Demo
```bash
py demo.py
```

## 🎯 Semua Command yang Tersedia

### Demo & Testing
```bash
py demo.py          # Demo interaktif sistem
py main.py          # Analisis lengkap Bitcoin
py test_system.py   # Test semua komponen
```

### Batch Files (Alternatif)
```bash
.\run_demo_simple.bat    # Demo dengan batch file
.\run_main_simple.bat    # Main analysis dengan batch file
.\run_test_simple.bat    # Test dengan batch file
```

## 📊 Output yang Diharapkan

Ketika menjalankan `py demo.py`, Anda akan melihat:

```
🚀 Crypto ML System Demo
========================================
📊 Generating synthetic Bitcoin data...
✅ Generated 720 price points

📈 Technical Analysis:
   Current Price: $39996.99
   RSI: 49.6 (Neutral)
   MACD Signal: Bullish

🤖 ML Price Prediction:
   Model R² Score: 1.000
   Predicted next price: $39953.49

🎯 Trading Signal Generation:
   Signal Accuracy: 0.900
   Current Signal: HOLD

🔄 Market Regime Analysis:
   Current Market Regime: 1 (Normal Market)

🎉 Demo completed successfully!
```

## 🔧 Troubleshooting

### Jika `py` tidak dikenali:
```bash
# Gunakan path lengkap Python
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe demo.py
```

### Jika ada error module:
```bash
# Install ulang dependencies
py -m pip install --user numpy requests matplotlib
```

### Jika permission error:
```bash
# Jalankan sebagai Administrator atau gunakan --user flag
```

## 🎉 Selesai!

Sistem ML crypto analysis siap digunakan dengan pure NumPy!

**Fitur yang tersedia:**
- ✅ Technical Analysis (RSI, MACD, Bollinger Bands, dll)
- ✅ ML Price Prediction
- ✅ Trading Signal Generation  
- ✅ Market Regime Analysis
- ✅ Semua menggunakan pure NumPy tanpa framework ML!
