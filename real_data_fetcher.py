"""
Real-time Cryptocurrency Data Fetcher
Fetch real cryptocurrency data from multiple sources
"""
import requests
import json
import time
import numpy as np
from datetime import datetime
import websocket
import threading

class RealTimeCryptoFetcher:
    """Real-time cryptocurrency data fetcher from multiple APIs"""
    
    def __init__(self):
        # Multiple API endpoints for redundancy
        self.apis = {
            'binance': {
                'base_url': 'https://api.binance.com/api/v3',
                'websocket': 'wss://stream.binance.com:9443/ws/',
                'symbols': {
                    'bitcoin': 'BTCUSDT',
                    'ethereum': 'ETHUSDT',
                    'cardano': 'ADAUSDT',
                    'solana': 'SOLUSDT'
                }
            },
            'coinbase': {
                'base_url': 'https://api.coinbase.com/v2',
                'symbols': {
                    'bitcoin': 'BTC-USD',
                    'ethereum': 'ETH-USD',
                    'cardano': 'ADA-USD',
                    'solana': 'SOL-USD'
                }
            },
            'kraken': {
                'base_url': 'https://api.kraken.com/0/public',
                'symbols': {
                    'bitcoin': 'XBTUSD',
                    'ethereum': 'ETHUSD',
                    'cardano': 'ADAUSD',
                    'solana': 'SOLUSD'
                }
            },
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'symbols': {
                    'bitcoin': 'bitcoin',
                    'ethereum': 'ethereum',
                    'cardano': 'cardano',
                    'solana': 'solana'
                }
            }
        }
        
        self.current_prices = {}
        self.price_history = {}
        self.websocket_connections = {}
        
    def get_real_price_binance(self, symbol):
        """Get real-time price from Binance API"""
        try:
            binance_symbol = self.apis['binance']['symbols'].get(symbol, 'BTCUSDT')
            url = f"{self.apis['binance']['base_url']}/ticker/price"
            params = {'symbol': binance_symbol}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            
            return {
                'price': float(data['price']),
                'symbol': symbol,
                'source': 'binance',
                'timestamp': datetime.now(),
                'success': True
            }
            
        except Exception as e:
            print(f"Binance API error for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_real_price_coinbase(self, symbol):
        """Get real-time price from Coinbase API"""
        try:
            coinbase_symbol = self.apis['coinbase']['symbols'].get(symbol, 'BTC-USD')
            url = f"{self.apis['coinbase']['base_url']}/exchange-rates"
            params = {'currency': coinbase_symbol.split('-')[0]}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            
            usd_rate = float(data['data']['rates']['USD'])
            
            return {
                'price': usd_rate,
                'symbol': symbol,
                'source': 'coinbase',
                'timestamp': datetime.now(),
                'success': True
            }
            
        except Exception as e:
            print(f"Coinbase API error for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_real_price_kraken(self, symbol):
        """Get real-time price from Kraken API"""
        try:
            kraken_symbol = self.apis['kraken']['symbols'].get(symbol, 'XBTUSD')
            url = f"{self.apis['kraken']['base_url']}/Ticker"
            params = {'pair': kraken_symbol}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            
            if data['error']:
                raise Exception(f"Kraken API error: {data['error']}")
            
            ticker_data = list(data['result'].values())[0]
            price = float(ticker_data['c'][0])  # Last trade price
            
            return {
                'price': price,
                'symbol': symbol,
                'source': 'kraken',
                'timestamp': datetime.now(),
                'success': True,
                'volume': float(ticker_data['v'][1]),  # 24h volume
                'high': float(ticker_data['h'][1]),    # 24h high
                'low': float(ticker_data['l'][1])      # 24h low
            }
            
        except Exception as e:
            print(f"Kraken API error for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_real_price_coingecko(self, symbol):
        """Get real-time price from CoinGecko API (free tier)"""
        try:
            coingecko_symbol = self.apis['coingecko']['symbols'].get(symbol, 'bitcoin')
            url = f"{self.apis['coingecko']['base_url']}/simple/price"
            params = {
                'ids': coingecko_symbol,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            
            coin_data = data[coingecko_symbol]
            
            return {
                'price': float(coin_data['usd']),
                'symbol': symbol,
                'source': 'coingecko',
                'timestamp': datetime.now(),
                'success': True,
                'change_24h': coin_data.get('usd_24h_change', 0),
                'volume_24h': coin_data.get('usd_24h_vol', 0)
            }
            
        except Exception as e:
            print(f"CoinGecko API error for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_best_price(self, symbol):
        """Get best available real-time price from multiple sources"""
        sources = [
            self.get_real_price_binance,
            self.get_real_price_kraken,
            self.get_real_price_coinbase,
            self.get_real_price_coingecko
        ]
        
        results = []
        
        for source_func in sources:
            try:
                result = source_func(symbol)
                if result.get('success'):
                    results.append(result)
                    # Return first successful result for speed
                    return result
            except Exception as e:
                print(f"Error with source {source_func.__name__}: {e}")
                continue
        
        # If all sources fail, return error
        return {
            'success': False,
            'error': 'All price sources failed',
            'symbol': symbol,
            'timestamp': datetime.now()
        }
    
    def get_detailed_market_data(self, symbol):
        """Get detailed market data including OHLCV"""
        try:
            # Try Binance first for detailed data
            binance_symbol = self.apis['binance']['symbols'].get(symbol, 'BTCUSDT')
            
            # Get 24hr ticker statistics
            url = f"{self.apis['binance']['base_url']}/ticker/24hr"
            params = {'symbol': binance_symbol}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            
            return {
                'symbol': symbol,
                'price': float(data['lastPrice']),
                'open_24h': float(data['openPrice']),
                'high_24h': float(data['highPrice']),
                'low_24h': float(data['lowPrice']),
                'volume_24h': float(data['volume']),
                'price_change_24h': float(data['priceChange']),
                'price_change_percent_24h': float(data['priceChangePercent']),
                'bid_price': float(data['bidPrice']),
                'ask_price': float(data['askPrice']),
                'timestamp': datetime.now(),
                'source': 'binance',
                'success': True
            }
            
        except Exception as e:
            print(f"Detailed data error for {symbol}: {e}")
            # Fallback to simple price
            return self.get_best_price(symbol)
    
    def get_multiple_prices(self, symbols):
        """Get real-time prices for multiple symbols"""
        results = {}
        
        for symbol in symbols:
            results[symbol] = self.get_best_price(symbol)
            time.sleep(0.1)  # Small delay to avoid rate limiting
        
        return results
    
    def start_websocket_stream(self, symbol, callback):
        """Start WebSocket stream for real-time updates (Binance)"""
        try:
            binance_symbol = self.apis['binance']['symbols'].get(symbol, 'btcusdt')
            stream_name = f"{binance_symbol.lower()}@ticker"
            ws_url = f"{self.apis['binance']['websocket']}{stream_name}"
            
            def on_message(ws, message):
                try:
                    data = json.loads(message)
                    price_data = {
                        'symbol': symbol,
                        'price': float(data['c']),  # Current price
                        'volume': float(data['v']),  # 24h volume
                        'change_24h': float(data['P']),  # 24h change %
                        'high_24h': float(data['h']),
                        'low_24h': float(data['l']),
                        'timestamp': datetime.now(),
                        'source': 'binance_websocket'
                    }
                    callback(price_data)
                except Exception as e:
                    print(f"WebSocket message error: {e}")
            
            def on_error(ws, error):
                print(f"WebSocket error: {error}")
            
            def on_close(ws, close_status_code, close_msg):
                print(f"WebSocket closed for {symbol}")
            
            def on_open(ws):
                print(f"WebSocket opened for {symbol}")
            
            ws = websocket.WebSocketApp(ws_url,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close,
                                      on_open=on_open)
            
            # Run WebSocket in separate thread
            ws_thread = threading.Thread(target=ws.run_forever, daemon=True)
            ws_thread.start()
            
            self.websocket_connections[symbol] = ws
            
            return True
            
        except Exception as e:
            print(f"WebSocket setup error for {symbol}: {e}")
            return False
    
    def stop_websocket_stream(self, symbol):
        """Stop WebSocket stream for a symbol"""
        if symbol in self.websocket_connections:
            self.websocket_connections[symbol].close()
            del self.websocket_connections[symbol]
            print(f"WebSocket stopped for {symbol}")
    
    def get_historical_klines(self, symbol, interval='1m', limit=100):
        """Get historical kline/candlestick data"""
        try:
            binance_symbol = self.apis['binance']['symbols'].get(symbol, 'BTCUSDT')
            url = f"{self.apis['binance']['base_url']}/klines"
            params = {
                'symbol': binance_symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Convert to structured format
            klines = []
            for kline in data:
                klines.append({
                    'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            return {
                'symbol': symbol,
                'interval': interval,
                'data': klines,
                'success': True
            }
            
        except Exception as e:
            print(f"Historical data error for {symbol}: {e}")
            return {'success': False, 'error': str(e)}

def test_real_data_fetcher():
    """Test function for real data fetcher"""
    print("🧪 Testing Real-time Crypto Data Fetcher")
    print("=" * 50)
    
    fetcher = RealTimeCryptoFetcher()
    
    # Test single price
    print("📊 Testing single price fetch...")
    btc_data = fetcher.get_best_price('bitcoin')
    print(f"Bitcoin: {btc_data}")
    
    # Test detailed data
    print("\n📈 Testing detailed market data...")
    detailed_data = fetcher.get_detailed_market_data('bitcoin')
    print(f"Detailed Bitcoin: {detailed_data}")
    
    # Test multiple prices
    print("\n💰 Testing multiple prices...")
    symbols = ['bitcoin', 'ethereum', 'cardano']
    multi_data = fetcher.get_multiple_prices(symbols)
    for symbol, data in multi_data.items():
        if data.get('success'):
            print(f"{symbol.upper()}: ${data['price']:,.2f} ({data['source']})")
        else:
            print(f"{symbol.upper()}: Error - {data.get('error')}")
    
    # Test historical data
    print("\n📊 Testing historical data...")
    historical = fetcher.get_historical_klines('bitcoin', '1h', 24)
    if historical.get('success'):
        print(f"Historical data: {len(historical['data'])} points")
        latest = historical['data'][-1]
        print(f"Latest: Open=${latest['open']:.2f}, Close=${latest['close']:.2f}")
    
    print("\n✅ Real data fetcher test completed!")

if __name__ == "__main__":
    test_real_data_fetcher()
